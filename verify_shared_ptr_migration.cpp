#include <iostream>
#include <memory>
#include <queue>
#include <cstring>

// 模拟必要的类型定义
typedef char CHAR;
typedef unsigned int UINT32;
typedef unsigned long long UINT64;
typedef unsigned char UINT8;

#define OK 0
#define FAIL -1

// 模拟 FRAMEINFO_t 结构
typedef struct {
    UINT64 utctime;
    UINT8 flags;
} FRAMEINFO_t;

#define IPC_FRAME_FLAG_IFRAME 1

// 验证用的数据包结构（应该与 live.h 中的一致）
typedef struct _T_LIVE_STREAM_PACKET {
    UINT32 dataSize;                        // 数据大小
    UINT64 timestamp;                       // 时间戳
    UINT8  frameType;                       // 帧类型 (I帧/P帧等)
    UINT8  keyFrame;                        // 是否关键帧
    UINT32 sequence;                        // 序列号
    std::shared_ptr<CHAR[]> data;           // 智能指针管理的数据

    // 默认构造函数
    _T_LIVE_STREAM_PACKET() 
        : dataSize(0), timestamp(0), frameType(0), keyFrame(0), sequence(0), data(nullptr) {}

} T_LIVE_STREAM_PACKET;

// 简化的创建数据包函数（模拟 createPacket）
int createTestPacket(T_LIVE_STREAM_PACKET& packet, FRAMEINFO_t* hdr, void* data, UINT32 size) {
    // 安全检查
    if (size == 0 || !data) {
        return FAIL;
    }

    try {
        // 使用 shared_ptr 分配数据内存
        packet.data = std::shared_ptr<CHAR[]>(new CHAR[size]);
        
        // 复制数据
        memcpy(packet.data.get(), data, size);

        // 填充包信息
        packet.dataSize = size;
        packet.timestamp = hdr->utctime;
        packet.frameType = hdr->flags;
        packet.keyFrame = (hdr->flags == IPC_FRAME_FLAG_IFRAME) ? 1 : 0;
        packet.sequence = 1;
        
        return OK;
        
    } catch (const std::bad_alloc& e) {
        std::cout << "内存分配异常: " << e.what() << std::endl;
        return FAIL;
    }
}

// 验证函数
void verifySharedPtrMigration() {
    std::cout << "=== 验证 shared_ptr 迁移 ===" << std::endl;
    
    // 1. 验证数据结构大小
    std::cout << "1. 数据结构验证:" << std::endl;
    std::cout << "   sizeof(T_LIVE_STREAM_PACKET): " << sizeof(T_LIVE_STREAM_PACKET) << " 字节" << std::endl;
    std::cout << "   sizeof(std::shared_ptr<CHAR[]>): " << sizeof(std::shared_ptr<CHAR[]>) << " 字节" << std::endl;
    
    // 2. 验证基本功能
    std::cout << "\n2. 基本功能验证:" << std::endl;
    
    const char* testData = "Hello, shared_ptr!";
    FRAMEINFO_t hdr = {12345, IPC_FRAME_FLAG_IFRAME};
    
    T_LIVE_STREAM_PACKET packet;
    int ret = createTestPacket(packet, &hdr, (void*)testData, strlen(testData) + 1);
    
    if (ret == OK) {
        std::cout << "   ✅ 数据包创建成功" << std::endl;
        std::cout << "   数据大小: " << packet.dataSize << " 字节" << std::endl;
        std::cout << "   数据内容: " << packet.data.get() << std::endl;
        std::cout << "   引用计数: " << packet.data.use_count() << std::endl;
    } else {
        std::cout << "   ❌ 数据包创建失败" << std::endl;
        return;
    }
    
    // 3. 验证拷贝语义
    std::cout << "\n3. 拷贝语义验证:" << std::endl;
    {
        T_LIVE_STREAM_PACKET packet2 = packet;  // 拷贝
        std::cout << "   拷贝后原包引用计数: " << packet.data.use_count() << std::endl;
        std::cout << "   拷贝包引用计数: " << packet2.data.use_count() << std::endl;
        std::cout << "   数据内容一致性: " << (strcmp(packet.data.get(), packet2.data.get()) == 0 ? "✅ 一致" : "❌ 不一致") << std::endl;
    }
    std::cout << "   作用域结束后引用计数: " << packet.data.use_count() << std::endl;
    
    // 4. 验证移动语义
    std::cout << "\n4. 移动语义验证:" << std::endl;
    std::queue<T_LIVE_STREAM_PACKET> queue;
    
    std::cout << "   移动前引用计数: " << packet.data.use_count() << std::endl;
    queue.push(std::move(packet));
    std::cout << "   移动后原包引用计数: " << packet.data.use_count() << std::endl;
    
    if (!queue.empty()) {
        std::cout << "   队列中包的引用计数: " << queue.front().data.use_count() << std::endl;
        
        // 从队列取出
        T_LIVE_STREAM_PACKET retrieved = std::move(queue.front());
        queue.pop();
        std::cout << "   取出后引用计数: " << retrieved.data.use_count() << std::endl;
        std::cout << "   取出的数据内容: " << retrieved.data.get() << std::endl;
    }
    
    // 5. 验证内存管理
    std::cout << "\n5. 内存管理验证:" << std::endl;
    {
        std::vector<T_LIVE_STREAM_PACKET> packets;
        
        // 创建多个数据包
        for (int i = 0; i < 5; ++i) {
            T_LIVE_STREAM_PACKET p;
            std::string data = "Packet " + std::to_string(i);
            FRAMEINFO_t h = {(UINT64)(12345 + i), IPC_FRAME_FLAG_IFRAME};
            
            if (createTestPacket(p, &h, (void*)data.c_str(), data.length() + 1) == OK) {
                packets.push_back(p);  // 拷贝到vector
            }
        }
        
        std::cout << "   创建了 " << packets.size() << " 个数据包" << std::endl;
        if (!packets.empty()) {
            std::cout << "   第一个包的引用计数: " << packets[0].data.use_count() << std::endl;
        }
    }
    std::cout << "   vector 销毁后，内存应该已自动释放" << std::endl;
    
    std::cout << "\n=== 验证完成 ===" << std::endl;
}

int main() {
    std::cout << "shared_ptr 迁移验证程序" << std::endl;
    std::cout << "========================" << std::endl;
    
    verifySharedPtrMigration();
    
    std::cout << "\n如果所有测试都通过，说明 shared_ptr 迁移成功！" << std::endl;
    return 0;
}
