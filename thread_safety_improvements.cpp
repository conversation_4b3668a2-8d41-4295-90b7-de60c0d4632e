// 线程安全改进方案
// 针对网络回调函数的多线程安全处理

#include <QMutex>
#include <QMutexLocker>
#include <QReadWriteLock>
#include <QThread>
#include <QTimer>
#include <QQueue>
#include <QWaitCondition>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>

// 1. 线程安全的消息队列
class ThreadSafeMessageQueue {
private:
    struct Message {
        QByteArray data;
        int msgCode;
        qint64 timestamp;
        
        Message(const QByteArray& d, int code) 
            : data(d), msgCode(code), timestamp(QDateTime::currentMSecsSinceEpoch()) {}
    };
    
    mutable QMutex m_mutex;
    QQueue<Message> m_queue;
    QWaitCondition m_condition;
    std::atomic<bool> m_shutdown{false};
    static const int MAX_QUEUE_SIZE = 1000;
    
public:
    bool enqueue(const QByteArray& data, int msgCode) {
        if (m_shutdown.load()) {
            return false;
        }
        
        QMutexLocker locker(&m_mutex);
        
        // 防止队列过大
        while (m_queue.size() >= MAX_QUEUE_SIZE) {
            qWarning("Message queue full, removing oldest message");
            m_queue.dequeue();
        }
        
        m_queue.enqueue(Message(data, msgCode));
        m_condition.wakeOne();
        return true;
    }
    
    bool dequeue(QByteArray& data, int& msgCode, int timeoutMs = 1000) {
        QMutexLocker locker(&m_mutex);
        
        while (m_queue.isEmpty() && !m_shutdown.load()) {
            if (!m_condition.wait(&m_mutex, timeoutMs)) {
                return false;  // 超时
            }
        }
        
        if (m_shutdown.load() && m_queue.isEmpty()) {
            return false;
        }
        
        if (!m_queue.isEmpty()) {
            Message msg = m_queue.dequeue();
            data = msg.data;
            msgCode = msg.msgCode;
            return true;
        }
        
        return false;
    }
    
    void shutdown() {
        m_shutdown.store(true);
        QMutexLocker locker(&m_mutex);
        m_condition.wakeAll();
    }
    
    int size() const {
        QMutexLocker locker(&m_mutex);
        return m_queue.size();
    }
};

// 2. 线程安全的回调管理器
class NetworkCallbackManager : public QObject {
    Q_OBJECT
    
private:
    ThreadSafeMessageQueue m_messageQueue;
    QThread* m_processingThread;
    std::atomic<bool> m_running{false};
    
    // 回调函数注册表
    mutable QReadWriteLock m_callbackLock;
    QMap<int, std::function<void(const QString&)>> m_callbacks;
    
public:
    NetworkCallbackManager(QObject* parent = nullptr) 
        : QObject(parent), m_processingThread(nullptr) {
        
        // 创建专用的消息处理线程
        m_processingThread = new QThread(this);
        this->moveToThread(m_processingThread);
        
        connect(m_processingThread, &QThread::started, 
                this, &NetworkCallbackManager::processMessages);
        connect(m_processingThread, &QThread::finished, 
                this, &NetworkCallbackManager::cleanup);
    }
    
    ~NetworkCallbackManager() {
        stop();
    }
    
    void start() {
        if (!m_running.load()) {
            m_running.store(true);
            m_processingThread->start();
        }
    }
    
    void stop() {
        if (m_running.load()) {
            m_running.store(false);
            m_messageQueue.shutdown();
            
            if (m_processingThread && m_processingThread->isRunning()) {
                m_processingThread->quit();
                m_processingThread->wait(5000);  // 等待5秒
            }
        }
    }
    
    // 线程安全的回调注册
    void registerCallback(int msgCode, std::function<void(const QString&)> callback) {
        QWriteLocker locker(&m_callbackLock);
        m_callbacks[msgCode] = callback;
    }
    
    void unregisterCallback(int msgCode) {
        QWriteLocker locker(&m_callbackLock);
        m_callbacks.remove(msgCode);
    }
    
    // 从网络线程调用的安全接口
    bool handleNetworkMessage(const char* buf, int len, int msgCode) {
        if (!buf || len <= 0 || !m_running.load()) {
            return false;
        }
        
        try {
            QByteArray data(buf, len);
            return m_messageQueue.enqueue(data, msgCode);
        } catch (const std::exception& e) {
            qCritical("Failed to handle network message: %s", e.what());
            return false;
        }
    }

private slots:
    void processMessages() {
        qInfo("Network callback processing thread started");
        
        while (m_running.load()) {
            QByteArray data;
            int msgCode;
            
            if (m_messageQueue.dequeue(data, msgCode, 1000)) {
                processMessage(data, msgCode);
            }
        }
        
        qInfo("Network callback processing thread stopped");
    }
    
    void cleanup() {
        QWriteLocker locker(&m_callbackLock);
        m_callbacks.clear();
    }

private:
    void processMessage(const QByteArray& data, int msgCode) {
        try {
            // 安全的字符串转换
            QString message;
            if (data.isValidUtf8()) {
                message = QString::fromUtf8(data);
            } else {
                message = QString::fromLatin1(data);
                qWarning("Non-UTF8 data in message code %d", msgCode);
            }
            
            // 查找并调用回调函数
            QReadLocker locker(&m_callbackLock);
            auto it = m_callbacks.find(msgCode);
            if (it != m_callbacks.end()) {
                try {
                    it.value()(message);
                } catch (const std::exception& e) {
                    qCritical("Callback exception for message code %d: %s", msgCode, e.what());
                }
            } else {
                qDebug("No callback registered for message code %d", msgCode);
            }
            
        } catch (const std::exception& e) {
            qCritical("Failed to process message: %s", e.what());
        }
    }
};

// 3. 改进的MainWindow类
class ThreadSafeMainWindow : public QMainWindow {
    Q_OBJECT
    
private:
    static NetworkCallbackManager* s_callbackManager;
    static QMutex s_initMutex;
    static std::atomic<bool> s_initialized;
    
    // 实例相关的线程安全数据
    mutable QMutex m_dataMutex;
    QStringList m_messageHistory;
    std::atomic<int> m_messageCount{0};
    
public:
    ThreadSafeMainWindow(QWidget* parent = nullptr) 
        : QMainWindow(parent) {
        
        initializeCallbackManager();
        setupMessageHandlers();
    }
    
    ~ThreadSafeMainWindow() {
        // 清理资源
        if (s_callbackManager) {
            s_callbackManager->unregisterCallback(2177);
        }
    }
    
    // 线程安全的静态回调函数
    static void dev_msg_cb(void* net_handle, int msg_code, char* buf, int len, void* context) {
        // 参数验证
        if (!buf || len <= 0 || len > 1024*1024 || !context) {
            qWarning("Invalid callback parameters");
            return;
        }
        
        // 确保回调管理器已初始化
        if (!s_initialized.load() || !s_callbackManager) {
            qWarning("Callback manager not initialized");
            return;
        }
        
        // 委托给线程安全的管理器
        s_callbackManager->handleNetworkMessage(buf, len, msg_code);
    }

private:
    static void initializeCallbackManager() {
        if (s_initialized.load()) {
            return;
        }
        
        QMutexLocker locker(&s_initMutex);
        if (!s_initialized.load()) {
            s_callbackManager = new NetworkCallbackManager();
            s_callbackManager->start();
            s_initialized.store(true);
        }
    }
    
    void setupMessageHandlers() {
        if (!s_callbackManager) {
            return;
        }
        
        // 注册消息处理器
        s_callbackManager->registerCallback(2177, 
            [this](const QString& message) {
                this->handleSpecificMessage(message);
            });
    }
    
    void handleSpecificMessage(const QString& message) {
        // 确保在正确的线程中执行
        if (QThread::currentThread() != this->thread()) {
            QMetaObject::invokeMethod(this, 
                [this, message]() {
                    this->handleSpecificMessage(message);
                },
                Qt::QueuedConnection);
            return;
        }
        
        // 线程安全的数据更新
        {
            QMutexLocker locker(&m_dataMutex);
            m_messageHistory.append(message);
            
            // 限制历史记录大小
            while (m_messageHistory.size() > 1000) {
                m_messageHistory.removeFirst();
            }
        }
        
        m_messageCount.fetch_add(1);
        
        // 更新UI
        updateUI(message);
    }
    
    void updateUI(const QString& message) {
        // UI更新逻辑
        qDebug("Received message: %s", qPrintable(message));
        
        // 发射信号给UI组件
        emit messageReceived(message);
    }

signals:
    void messageReceived(const QString& message);

public:
    // 线程安全的状态查询
    int getMessageCount() const {
        return m_messageCount.load();
    }
    
    QStringList getMessageHistory() const {
        QMutexLocker locker(&m_dataMutex);
        return m_messageHistory;
    }
    
    // 静态清理函数
    static void cleanup() {
        QMutexLocker locker(&s_initMutex);
        if (s_callbackManager) {
            s_callbackManager->stop();
            delete s_callbackManager;
            s_callbackManager = nullptr;
            s_initialized.store(false);
        }
    }
};

// 静态成员定义
NetworkCallbackManager* ThreadSafeMainWindow::s_callbackManager = nullptr;
QMutex ThreadSafeMainWindow::s_initMutex;
std::atomic<bool> ThreadSafeMainWindow::s_initialized{false};

// 4. 使用RAII的网络资源管理
class NetworkResourceManager {
private:
    void* m_handle;
    bool m_valid;
    static QMutex s_resourceMutex;
    static QSet<void*> s_activeHandles;
    
public:
    explicit NetworkResourceManager(void* handle) 
        : m_handle(handle), m_valid(false) {
        
        if (handle) {
            QMutexLocker locker(&s_resourceMutex);
            if (s_activeHandles.contains(handle)) {
                qWarning("Handle already in use: %p", handle);
            } else {
                s_activeHandles.insert(handle);
                m_valid = true;
            }
        }
    }
    
    ~NetworkResourceManager() {
        if (m_valid && m_handle) {
            QMutexLocker locker(&s_resourceMutex);
            s_activeHandles.remove(m_handle);
        }
    }
    
    bool isValid() const { return m_valid; }
    void* getHandle() const { return m_valid ? m_handle : nullptr; }
};

QMutex NetworkResourceManager::s_resourceMutex;
QSet<void*> NetworkResourceManager::s_activeHandles;

#include "thread_safety_improvements.moc"
