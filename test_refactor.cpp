#include <iostream>
#include <memory>
#include <queue>
#include <cstring>

// 模拟必要的类型定义
typedef int INT32;
typedef unsigned int UINT32;
typedef unsigned long long UINT64;
typedef unsigned char UINT8;
typedef char CHAR;
typedef void* LPVOID;

#define OK 0
#define FAIL -1

// 模拟 FRAMEINFO_t 结构
typedef struct {
    UINT64 utctime;
    UINT8 flags;
} FRAMEINFO_t;

#define IPC_FRAME_FLAG_IFRAME 1

// 重构后的数据包结构 - 使用shared_ptr
typedef struct _T_LIVE_STREAM_PACKET {
    UINT32 dataSize;                        // 数据大小
    UINT64 timestamp;                       // 时间戳
    UINT8  frameType;                       // 帧类型 (I帧/P帧等)
    UINT8  keyFrame;                        // 是否关键帧
    UINT32 sequence;                        // 序列号
    std::shared_ptr<CHAR[]> data;           // shared_ptr智能指针管理的数据

    // 默认构造函数
    _T_LIVE_STREAM_PACKET()
        : dataSize(0), timestamp(0), frameType(0), keyFrame(0), sequence(0), data(nullptr) {}

} T_LIVE_STREAM_PACKET;

// 测试函数：创建数据包
INT32 createTestPacket(T_LIVE_STREAM_PACKET& packet, FRAMEINFO_t* hdr, void* data, UINT32 size) {
    if (size == 0 || !data) {
        return FAIL;
    }

    try {
        // 使用智能指针分配数据内存
        packet.data = std::shared_ptr<CHAR[]>(new CHAR[size]);

        // 复制数据
        memcpy(packet.data.get(), data, size);

        // 填充包信息
        packet.dataSize = size;
        packet.timestamp = hdr->utctime;
        packet.frameType = hdr->flags;
        packet.keyFrame = (hdr->flags == IPC_FRAME_FLAG_IFRAME) ? 1 : 0;
        packet.sequence = 1;

        return OK;

    } catch (const std::bad_alloc& e) {
        std::cout << "内存分配异常: " << e.what() << std::endl;
        return FAIL;
    }
}

// 测试函数：模拟队列操作
void testQueueOperations() {
    std::queue<T_LIVE_STREAM_PACKET> dataQueue;
    
    // 创建测试数据
    const char* testData = "Hello, World!";
    FRAMEINFO_t hdr = {12345, IPC_FRAME_FLAG_IFRAME};
    
    // 创建数据包并添加到队列
    T_LIVE_STREAM_PACKET packet;
    INT32 ret = createTestPacket(packet, &hdr, (void*)testData, strlen(testData) + 1);

    if (ret == OK && packet.data) {
        std::cout << "成功创建数据包，大小: " << packet.dataSize << std::endl;
        std::cout << "数据内容: " << packet.data.get() << std::endl;
        
        // 移动到队列
        dataQueue.push(std::move(packet));
        std::cout << "数据包已添加到队列，队列大小: " << dataQueue.size() << std::endl;
        
        // 从队列取出
        if (!dataQueue.empty()) {
            T_LIVE_STREAM_PACKET retrievedPacket = std::move(dataQueue.front());
            dataQueue.pop();
            
            std::cout << "从队列取出数据包，大小: " << retrievedPacket.dataSize << std::endl;
            std::cout << "取出的数据内容: " << retrievedPacket.data.get() << std::endl;
        }
    } else {
        std::cout << "创建数据包失败" << std::endl;
    }
}

// 测试函数：模拟引用传递
INT32 popStreamData(T_LIVE_STREAM_PACKET& packet, std::queue<T_LIVE_STREAM_PACKET>& queue) {
    if (queue.empty()) {
        return FAIL;
    }
    
    packet = std::move(queue.front());
    queue.pop();
    return OK;
}

void testReferenceInterface() {
    std::queue<T_LIVE_STREAM_PACKET> dataQueue;
    
    // 创建测试数据
    const char* testData = "Reference Test Data";
    FRAMEINFO_t hdr = {67890, IPC_FRAME_FLAG_IFRAME};
    
    // 创建并添加数据包
    T_LIVE_STREAM_PACKET packet;
    INT32 ret = createTestPacket(packet, &hdr, (void*)testData, strlen(testData) + 1);
    if (ret == OK) {
        dataQueue.push(std::move(packet));
    }
    
    // 使用引用接口取出数据
    T_LIVE_STREAM_PACKET outputPacket;
    INT32 result = popStreamData(outputPacket, dataQueue);
    
    if (result == OK) {
        std::cout << "引用接口测试成功" << std::endl;
        std::cout << "取出数据: " << outputPacket.data.get() << std::endl;
    } else {
        std::cout << "引用接口测试失败" << std::endl;
    }
}

int main() {
    std::cout << "=== 测试重构后的数据结构 ===" << std::endl;
    
    std::cout << "\n1. 测试队列操作:" << std::endl;
    testQueueOperations();
    
    std::cout << "\n2. 测试引用传递接口:" << std::endl;
    testReferenceInterface();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
