// MainWindow::dev_msg_cb 修复示例
// 解决QString内存管理和多线程安全问题

#include <QMutex>
#include <QMutexLocker>
#include <QByteArray>
#include <QString>
#include <QMetaObject>
#include <QThread>

class MainWindow : public QMainWindow {
    Q_OBJECT

private:
    static QMutex s_callbackMutex;  // 静态互斥锁保护回调函数

public slots:
    void handleNetworkMessage(const QByteArray& data, int msgCode);

signals:
    void networkMessageReceived(const QByteArray& data, int msgCode);

public:
    // 修复后的回调函数
    static void dev_msg_cb(void* net_handle, int msg_code, char* buf, int len, void* context) {
        // 1. 参数验证
        if (!buf || len <= 0 || len > 65536) {  // 防止异常长度
            qWarning("dev_msg_cb: Invalid parameters - buf=%p, len=%d", buf, len);
            return;
        }

        if (!context) {
            qWarning("dev_msg_cb: Invalid context pointer");
            return;
        }

        // 2. 线程安全保护
        QMutexLocker locker(&s_callbackMutex);

        try {
            MainWindow* mainWindow = static_cast<MainWindow*>(context);
            
            // 3. 安全的数据复制 - 使用QByteArray而不是QString
            QByteArray safeData;
            safeData.reserve(len);  // 预分配内存
            safeData.append(buf, len);  // 深拷贝数据
            
            // 4. 跨线程安全传递 - 使用Qt的元对象系统
            if (QThread::currentThread() != mainWindow->thread()) {
                // 异步调用，避免跨线程直接操作
                QMetaObject::invokeMethod(
                    mainWindow,
                    "handleNetworkMessage",
                    Qt::QueuedConnection,  // 队列连接，线程安全
                    Q_ARG(QByteArray, safeData),
                    Q_ARG(int, msg_code)
                );
            } else {
                // 同线程直接调用
                mainWindow->handleNetworkMessage(safeData, msg_code);
            }
            
        } catch (const std::exception& e) {
            qCritical("dev_msg_cb exception: %s", e.what());
        } catch (...) {
            qCritical("dev_msg_cb unknown exception");
        }
    }

private:
    void handleNetworkMessage(const QByteArray& data, int msgCode) {
        // 在UI线程中安全处理消息
        QMutexLocker locker(&s_callbackMutex);
        
        try {
            // 5. 安全的字符串转换
            QString message;
            
            // 检查数据有效性
            if (data.isEmpty()) {
                qWarning("Empty network message received");
                return;
            }
            
            // 尝试UTF-8解码，失败则使用Latin-1
            if (data.isValidUtf8()) {
                message = QString::fromUtf8(data);
            } else {
                message = QString::fromLatin1(data);
                qWarning("Non-UTF8 data received, using Latin-1 fallback");
            }
            
            // 6. 限制字符串长度，防止内存问题
            if (message.length() > 10000) {
                message = message.left(10000) + "...[truncated]";
                qWarning("Message truncated due to excessive length");
            }
            
            // 7. 安全的UI更新
            processNetworkMessage(message, msgCode);
            
        } catch (const std::exception& e) {
            qCritical("handleNetworkMessage exception: %s", e.what());
        }
    }
    
    void processNetworkMessage(const QString& message, int msgCode) {
        // 具体的消息处理逻辑
        switch (msgCode) {
            case 2177:  // 从堆栈信息中的消息代码
                handleSpecificMessage(message);
                break;
            default:
                qDebug("Unknown message code: %d", msgCode);
                break;
        }
    }
    
    void handleSpecificMessage(const QString& message) {
        // 处理特定消息类型
        // 确保所有QString操作都在UI线程中进行
        if (QThread::currentThread() != this->thread()) {
            qWarning("handleSpecificMessage called from wrong thread");
            return;
        }
        
        // 安全的UI更新操作
        // ...
    }
};

// 静态成员定义
QMutex MainWindow::s_callbackMutex;

// 替代方案：使用智能指针和RAII
class SafeNetworkCallback {
private:
    std::shared_ptr<QByteArray> m_data;
    int m_msgCode;
    
public:
    SafeNetworkCallback(const char* buf, int len, int msgCode) 
        : m_msgCode(msgCode) {
        if (buf && len > 0) {
            m_data = std::make_shared<QByteArray>(buf, len);
        }
    }
    
    bool isValid() const {
        return m_data && !m_data->isEmpty();
    }
    
    QString toSafeString() const {
        if (!isValid()) return QString();
        
        // 安全的字符串转换
        if (m_data->isValidUtf8()) {
            return QString::fromUtf8(*m_data);
        } else {
            return QString::fromLatin1(*m_data);
        }
    }
    
    int messageCode() const { return m_msgCode; }
};

// 使用示例：
void improved_dev_msg_cb(void* net_handle, int msg_code, char* buf, int len, void* context) {
    SafeNetworkCallback callback(buf, len, msg_code);
    
    if (!callback.isValid()) {
        qWarning("Invalid network callback data");
        return;
    }
    
    MainWindow* mainWindow = static_cast<MainWindow*>(context);
    if (!mainWindow) {
        qWarning("Invalid MainWindow context");
        return;
    }
    
    // 线程安全的消息传递
    QMetaObject::invokeMethod(
        mainWindow,
        [mainWindow, callback]() {
            QString message = callback.toSafeString();
            mainWindow->processNetworkMessage(message, callback.messageCode());
        },
        Qt::QueuedConnection
    );
}
