#pragma once

#include <string>
#include <vector>
#include <map>
#include <pthread.h>
#include <list>
#include <stdint.h>
#include <atomic>
#include "buffer_manager.h"
#include "vs_comm_def.h"
#include "vs_media_file.h"
#include "Thread/Lock.hpp"
#include "Thread/Cond.hpp"
#include "disk_manager.h"

#define REC_FILE_TIME         	(1*60)    	// 每个录像文件时长(秒)
#define REC_FILE_EXT          	"dat"    	// 录像文件扩展名
#define REC_IDX_EXT           	"idx"      	// 索引文件扩展名

#define ALL_REC_FILE_TIME     	(5*60)      // 全天录像文件时长(秒)
#define EVENT_REC_FILE_TIME   	(5*60)     	// 事件录像文件总时长(秒)
#define EVENT_REC_ADD_TIME    	(20)        // 事件录像文件累加时长(秒)

#define MAX_NO_STREAM_COUNT    	60 			// 最大无流次数阈值

// 常量定义
#define RECORD_CHECK_INTERVAL 	1000   		// 录像检查间隔（毫秒）
#define DEFAULT_EVENT_DURATION 	10

// 录像状态定义
typedef enum {
    REC_STATE_OPEN,     // 开始录像
    REC_STATE_SWITCH,   // 切换录像
    REC_STATE_CLOSE     // 结束录像
} REC_STATE_E;



// 录像事件结构体
typedef struct {
    REC_STATE_E      recState;      // 录像状态
    uint8_t          eventType;     // 事件类型 (使用AET_EVENT_xxx常量)
    uint64_t         abs_end_time;  // 结束绝对时间
    int              channel;       // 通道号
} REC_EVENT_S;

// 录像状态结构体
typedef struct {
    bool     is_recording;        // 是否正在录像
    bool     is_event_recording;  // 是否正在事件录像
    int      channel;             // 通道号
    uint64_t start_time;          // 开始时间
    uint64_t end_time;            // 结束时间
    uint8_t  event_type;          // 事件类型 (使用AET_EVENT_xxx常量)
} RECORD_STATUS;

// 录像状态回调函数类型
typedef void (*RECORD_STATUS_CALLBACK)(int channel, RECORD_STATUS* status);

// 录像文件对应的 I 帧索引
typedef struct {
    uint32_t utc_time;          // utc时间
    uint32_t time_stamp;        // 时间戳
    uint64_t file_offset;       // 帧在录像文件的位置
    uint32_t frame_size;        // 帧的原始大小
    uint8_t  frame_type;        // 帧类型 I P Audio
    uint8_t  event_type;        // 事件类型 0:普通录像 1:事件录像
    uint8_t  reserve[2];        // 预留 对齐
} __attribute__((packed)) T_INDEX_ENTRY;


// 录像通道类
class RecordChannel {
public:
    RecordChannel(int channel_id);
    ~RecordChannel();
    
    // === 传统同步接口（保留兼容性） ===
    void start();
    void stop();
    bool isRunning() const { return running; }
    void setEventType(uint8_t event_type);

    // === 优化后的非阻塞接口 ===

    // 非阻塞启动录像（使用TryLock避免阻塞）
    bool startNonBlocking();

    // 非阻塞停止录像
    bool stopNonBlocking();

    // 非阻塞事件录像
    void startEventNonBlocking(uint8_t eventType, int duration = 10);
    void stopEventNonBlocking(uint8_t eventType);

    // 状态查询（非阻塞）
    bool isRecordingNonBlocking() const;
    bool hasActiveEventNonBlocking(uint8_t eventType) const;

    // 获取当前录像文件信息
    bool getCurrentRecordFiles(std::string& video_file, std::string& idx_file) const;
    
    // 预录像相关 - 仅保留配置接口，实际使用vs_media_file.cpp中的预录像功能
    void setPreRecordParams(bool enabled, int duration);
    bool isPreRecordEnabled() const;
    int getPreRecordDuration() const;
    
    // 录像模式设置
    void setFullTimeRecording(bool enabled);
    void setEventOnlyMode(bool enabled);
    bool isFullTimeRecording() const { return is_full_time_recording; }
    bool isEventOnlyMode() const { return is_event_only_mode; }
    
    void event_start(int event_type, int duration); // 事件录像打标记
    
    // 检查是否有活跃事件
    bool hasActiveEvent(time_t now) const;
    
    // 获取当前活跃事件类型，如果没有活跃事件则返回0(RT_NORMAL)
    uint8_t getActiveEventType(time_t now) const;

	void generateDateDir(char* path, size_t size, time_t now);

	// 磁盘切换完整性保护
    void prepareForDiskSwitch();
    bool isFileWriteInProgress() const;
    void forceCloseCurrentFile();
    void resetDiskSwitchState();

    // 平滑过渡机制
    bool checkAndExecutePendingDiskSwitch();
    void setDiskSwitchDeferred(bool deferred);

private:
    static void* recordThreadFunc(void* arg);
    void recordThread();

    // === 重构后的辅助函数 ===
    // 写入帧数据到录像文件
    bool writeFrameData(T_FRAME_HEADER* frame_hdr, CHAR* buffer, uint64_t& out_offset);

    // 创建并写入索引条目
    bool writeIndexEntry(T_FRAME_HEADER* frame_hdr, uint64_t file_offset, time_t local_ts);

    // 处理I帧同步操作（磁盘同步和更新结束时间）
    void handleIFrameSync(T_FRAME_HEADER* frame_hdr, time_t local_ts);

    // 创建新文件的通用处理（包含错误处理）
    bool createNewFileWithErrorHandling(time_t now);
    
    // 文件操作相关函数
    std::string generateFilename(time_t t, const char* ext);
    void ensureDirExist(const std::string& filename);
    void insertRecordStart(const std::string& video_name, const std::string& idx_name, time_t start_time);
    void updateRecordEnd(const std::string& video_name, time_t end_time);
    
    // 分离出的功能函数
    void closeCurrentFile(time_t now);
    bool handleStreamError(INT32 ret, time_t now);
	bool checkDateChange(time_t now, T_DATETIME *last_tm) ;
    bool checkFileSwitch(time_t now, time_t last_check_time, bool is_day_changed);
    bool createNewFile(time_t now);
    bool processFrame(T_FRAME_HEADER *frame_hdr, CHAR *buffer, time_t now);

    

    int chn;                    // 通道号
    pthread_t thread_id;        // 线程ID
    bool running;               // 运行状态
    
    int video_fd;               // 视频文件描述符
    int idx_fd;                 // 索引文件描述符
    uint8_t cur_event_type;     // 当前事件类型
    uint8_t previous_event_type; // 上一个事件类型，用于检测事件类型变化
    time_t file_start_time;     // 文件开始时间
    time_t file_end_time;       // 当前录像文件的实际结束时间
    bool check_next_frame;      // 是否检查下一帧
    bool day_changed;           // 日期是否已变化
    bool need_switch_file;      // 是否需要切换文件
    time_t iframe_wait_start_time; // 开始等待I帧的时间
    time_t real_end_time;       // 文件实际结束时间
    
    // 无流检测相关
    int no_stream_count;        // 连续未获取到流的次数
    
    // I帧等待相关
    bool wait_for_iframe;       // 等待I帧标志，确保新文件的第一帧是I帧
    
    // 当前录像文件信息
    std::string last_video_name;                // 最后一个视频文件名
    std::string last_idx_name;                  // 最后一个索引文件名
    
    // 预录像相关
    bool pre_record_enabled;                    // 是否启用预录像
    int pre_record_duration;                    // 预录像时长(秒)
    
    // 事件等待相关
    int event_wait_count;                       // 事件等待计数器
    
    // 录像模式相关
    bool is_full_time_recording;                // 是否开启全天录像
    bool is_event_only_mode;                    // 是否仅事件录像模式
    time_t pre_record_start_time;               // 预录像开始时间
    bool pre_record_active;                     // 预录像是否激活
    bool start_time_modified;                   // 开始时间是否被修改
    
    TCSLock lock;                               // 线程互斥锁
    TCondition cond;                            // 条件变量

	struct EventMark {
        int event_type;
        time_t start_time;
        time_t end_time;
    };
    std::vector<EventMark> event_marks; // 事件标记队列

    // === 非阻塞操作支持成员变量 ===
    mutable TCSLock m_nonBlockingLock;              // 非阻塞操作锁（使用TryLock）
    std::atomic<bool> m_recordingState{false};      // 录像状态（原子操作）

    // 事件管理（简化版本）
    struct SimpleEventInfo {
        uint8_t type;
        time_t startTime;
        time_t endTime;
        bool active;
    };
    std::map<uint8_t, SimpleEventInfo> m_activeEvents; // 活跃事件映射
    mutable TCSLock m_eventLock;                    // 事件锁

    // 磁盘切换完整性保护
    volatile bool m_diskSwitchPending;       // 磁盘切换待处理标志
    volatile bool m_fileWriteInProgress;     // 文件写入进行中标志
    mutable TCSLock m_diskSwitchLock;        // 磁盘切换锁

    // 平滑过渡机制状态
    volatile bool m_diskSwitchDeferred;      // 磁盘切换延迟标志
};

// 录像事件管理类
class RecordEventManager {
public:
    static RecordEventManager* getInstance();
    ~RecordEventManager();
    
    // 初始化和反初始化
    bool init();
    void uninit();
    
    // 事件管理
    void setEvent(int channel, REC_STATE_E recState, uint8_t eventType, uint64_t abs_end_time);
    bool getEvent(int channel, REC_EVENT_S& event);
    bool pushEventMsg(int channel, uint8_t eventType, int delay_sec_close);
    
    // 新增：事件录像续期管理
    void startEventRecord(int channel, uint8_t eventType, int duration);
    void stopEventRecord(int channel, uint8_t eventType);
    bool isEventRecording(int channel, uint8_t eventType);
    void extendEventRecord(int channel, uint8_t eventType, int additional_duration = 10);
    
    // 获取系统时间
    static uint64_t getSystemUptimeMs();
    
    // 检查初始化状态
    bool isInitialized() const { return m_initialized; }

    // 清理指定通道的所有事件
    void clearChannelEvents(int channel);

	// 通道管理辅助函数
    void ensureChannelExists(int channel);        // 确保通道事件队列存在（按需创建）
	std::vector<int> getActiveChannels() const;   // 获取所有活跃的通道ID列表

private:
    RecordEventManager();
    bool isValidChannel(int channel) const;
    void checkAndEndExpiredEvents();  // 检查并结束过期事件

    // 事件录像状态
    typedef struct {
        uint8_t event_type;           // 事件类型
        uint64_t start_time;          // 开始时间
        uint64_t end_time;            // 结束时间
        bool is_active;               // 是否活跃
        int duration;                 // 持续时间
    } EVENT_RECORD_STATUS;
    
    // 事件队列
    typedef struct {
        std::list<REC_EVENT_S> event_list;   // 事件列表
        TCSLock mutex;                       // 互斥锁
        bool initialized;                    // 是否已初始化
        std::map<uint8_t, EVENT_RECORD_STATUS> event_status;  // 事件状态映射
    } CHANNEL_EVENT_QUEUE;
    
    // 🚀 架构重构：使用map支持随机通道ID
    std::map<int, CHANNEL_EVENT_QUEUE> m_eventQueues;  // 通道ID -> 事件队列映射
    mutable TCSLock m_eventQueuesLock;                  // 事件队列映射保护锁

    bool m_initialized;
    pthread_t m_check_thread;         // 检查线程
    bool m_check_thread_running;      // 检查线程运行状态
    TCSLock m_check_mutex;            // 检查线程互斥锁
    
    static RecordEventManager* m_instance;
    
    // 检查线程函数
    static void* checkThreadFunc(void* arg);
};

// 录像管理类
class RecordManager {
public:
    RecordManager(int channel_id);
    ~RecordManager() {}
    
    bool startRecord(UINT32 streamMask);
    bool stopRecord();
    
private:
    int chn;                // 通道号
};

// 主功能函数
bool record_mgr_save_jpg(std::string& file_name, int device_id, const char* buff, int buff_size);


// 外部API函数声明，只在record.cpp中实现
// 开始事件录像
VOID start_event(INT32 chn, UINT8 event_type, INT32 dur_time = DEFAULT_EVENT_DURATION);

// 停止事件录像
VOID stop_event(INT32 chn, UINT8 event_type);

// 开启录像 full_time_recording=TRUE 表示 全天录像 FALSE 表示 事件录像
VOID start_record_channel(INT32 chn, BOOL full_time_recording);

// 关闭录像
VOID stop_record_channel(INT32 chn);

// 关闭所有通道的录像，用于在格式化磁盘前调用（优化版本，快速响应）
VOID stop_all_record_channels();

// 启动所有通道的录像
VOID start_all_record_channels();

// 录像相关函数声明
void init_record_manager();
void deinit_record_manager();

// 获取当前工作的磁盘路径
std::string get_active_disk_path();

// 录像文件完整性保护接口
bool is_record_channel_running(int channelId);
bool is_any_recording_in_progress();
void prepare_all_channels_for_disk_switch();
bool wait_for_all_recordings_complete(int timeoutSeconds);
void reset_all_channels_disk_switch_state();

// 平滑过渡机制接口
void set_all_channels_disk_switch_deferred(bool deferred);
bool check_and_execute_pending_disk_switches();


