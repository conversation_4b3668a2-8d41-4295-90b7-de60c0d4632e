// SIGABRT vs SIGSEGV 详细解释
// 为什么是程序主动abort()而不是段错误

#include <signal.h>
#include <cstdlib>
#include <iostream>
#include <QString>

/*
信号类型对比：

SIGSEGV (Segmentation Violation):
- 由操作系统内核发送
- 当程序访问无效内存地址时触发
- 例如：解引用空指针、访问未映射内存、权限违规

SIGABRT (Abort):
- 由程序自身调用abort()函数触发
- 表示程序检测到内部错误并主动终止
- 通常用于断言失败、内存管理错误检测等
*/

// 1. SIGABRT的产生机制
class SIGABRTMechanism {
public:
    // 解释abort()的调用路径
    static void explainAbortCallPath() {
        /*
        基于GDB堆栈的abort()调用路径：
        
        #0  raise() from /lib64/libc.so.6
        #1  abort() from /lib64/libc.so.6
        #2  ?? () from /lib64/libc.so.6          <- 可能是__malloc_assert
        #3  ?? () from /lib64/libc.so.6          <- 可能是malloc相关函数
        #4  ?? () from /lib64/libc.so.6          <- 可能是free()或realloc()
        #5  QTypedArrayData<unsigned short>::deallocate()
        
        调用序列分析：
        1. QString析构调用deallocate()
        2. deallocate()调用系统的free()函数
        3. free()检测到堆损坏或双重释放
        4. 内存分配器调用内部的断言函数
        5. 断言函数调用abort()
        6. abort()调用raise(SIGABRT)
        */
        
        // 模拟abort()的调用
        demonstrateAbortCall();
    }
    
private:
    static void demonstrateAbortCall() {
        std::cout << "Demonstrating abort() call mechanism:" << std::endl;
        
        // 模拟内存分配器检测到错误
        bool heap_corruption_detected = true;
        
        if (heap_corruption_detected) {
            std::cout << "Memory allocator detected corruption" << std::endl;
            std::cout << "Calling abort() to terminate program safely" << std::endl;
            
            // 在实际情况下，这里会调用abort()
            // abort();  // 这会终止程序
            
            std::cout << "abort() would be called here" << std::endl;
        }
    }
};

// 2. 内存分配器的安全检查
class MemoryAllocatorChecks {
public:
    // 解释为什么内存分配器会调用abort()
    static void explainAllocatorChecks() {
        /*
        现代内存分配器（如glibc的malloc）包含多种安全检查：
        
        1. 双重释放检测
        2. 堆元数据完整性检查
        3. 内存对齐验证
        4. 魔数验证
        5. 链表结构完整性检查
        
        当检测到这些错误时，分配器会：
        1. 记录错误信息
        2. 调用abort()主动终止程序
        3. 防止进一步的内存损坏
        */
        
        demonstrateAllocatorChecks();
    }
    
private:
    static void demonstrateAllocatorChecks() {
        /*
        模拟内存分配器的检查过程：
        */
        
        // 模拟堆块结构
        struct HeapBlock {
            uint32_t magic;      // 魔数
            size_t size;         // 块大小
            HeapBlock* next;     // 链表指针
            HeapBlock* prev;     // 链表指针
            // 实际数据跟在后面
        };
        
        const uint32_t HEAP_MAGIC = 0xDEADBEEF;
        
        // 模拟free()函数的检查
        auto mock_free = [](void* ptr) {
            if (!ptr) return;  // 空指针检查
            
            // 获取堆块头部
            HeapBlock* block = (HeapBlock*)((char*)ptr - sizeof(HeapBlock));
            
            // 检查1：魔数验证
            if (block->magic != HEAP_MAGIC) {
                std::cout << "ERROR: Invalid magic number detected" << std::endl;
                std::cout << "Expected: 0x" << std::hex << HEAP_MAGIC << std::endl;
                std::cout << "Found: 0x" << std::hex << block->magic << std::endl;
                std::cout << "Calling abort() due to heap corruption" << std::endl;
                // abort();  // 实际会调用abort()
                return;
            }
            
            // 检查2：双重释放检测
            if (block->magic == 0xFEEDFACE) {  // 已释放的标记
                std::cout << "ERROR: Double free detected" << std::endl;
                std::cout << "Block at " << ptr << " already freed" << std::endl;
                std::cout << "Calling abort() due to double free" << std::endl;
                // abort();
                return;
            }
            
            // 检查3：链表完整性
            if (block->next && block->next->prev != block) {
                std::cout << "ERROR: Heap linked list corruption" << std::endl;
                std::cout << "Calling abort() due to list corruption" << std::endl;
                // abort();
                return;
            }
            
            // 标记为已释放
            block->magic = 0xFEEDFACE;
            
            std::cout << "Block freed successfully" << std::endl;
        };
        
        // 演示正常释放
        char dummy_data[64];
        HeapBlock* block = (HeapBlock*)dummy_data;
        block->magic = HEAP_MAGIC;
        block->next = nullptr;
        block->prev = nullptr;
        
        void* user_ptr = (char*)block + sizeof(HeapBlock);
        mock_free(user_ptr);  // 正常释放
        
        // 演示双重释放
        std::cout << "\nTesting double free:" << std::endl;
        mock_free(user_ptr);  // 双重释放，会触发错误
    }
};

// 3. Qt内存管理的特殊性
class QtMemoryManagement {
public:
    // 解释Qt为什么选择abort()而不是让程序崩溃
    static void explainQtAbortStrategy() {
        /*
        Qt选择abort()的原因：
        
        1. 可控的错误处理
           - abort()提供了清晰的错误信号
           - 可以生成core dump用于调试
           - 避免不可预测的内存损坏
        
        2. 防止数据损坏
           - 立即终止程序，防止进一步损坏
           - 保护用户数据不被破坏
           - 避免级联错误
        
        3. 调试友好
           - 提供明确的错误位置
           - 保留完整的调用栈
           - 便于问题定位
        */
        
        demonstrateQtErrorHandling();
    }
    
private:
    static void demonstrateQtErrorHandling() {
        /*
        Qt的错误处理策略示例：
        */
        
        // 模拟QTypedArrayData的deallocate函数
        auto qt_deallocate = [](void* data) {
            if (!data) return;
            
            // Qt的安全检查
            struct QArrayDataHeader {
                QAtomicInt ref;
                int size;
                uint alloc;
                // ...
            };
            
            QArrayDataHeader* header = (QArrayDataHeader*)data;
            
            // 检查引用计数合理性
            int ref_count = header->ref.load();
            if (ref_count < 0 || ref_count > 1000000) {
                std::cout << "Qt ERROR: Invalid reference count: " << ref_count << std::endl;
                std::cout << "This indicates memory corruption or threading issue" << std::endl;
                std::cout << "Qt calls abort() to prevent further damage" << std::endl;
                // abort();  // Qt会调用abort()
                return;
            }
            
            // 检查大小合理性
            if (header->size < 0 || header->size > 1000000000) {
                std::cout << "Qt ERROR: Invalid size: " << header->size << std::endl;
                std::cout << "Qt calls abort() to prevent buffer overflow" << std::endl;
                // abort();
                return;
            }
            
            std::cout << "Qt deallocate: checks passed, calling system free()" << std::endl;
            
            // 调用系统的free()，如果系统检测到问题，也会abort()
        };
        
        // 模拟正常情况
        struct MockQArrayData {
            QAtomicInt ref{1};
            int size{10};
            uint alloc{16};
        } mock_data;
        
        qt_deallocate(&mock_data);
        
        // 模拟异常情况
        std::cout << "\nTesting corrupted data:" << std::endl;
        mock_data.ref.store(-1);  // 损坏的引用计数
        qt_deallocate(&mock_data);
    }
};

// 4. SIGSEGV vs SIGABRT 的区别
class SignalComparison {
public:
    static void compareSignals() {
        /*
        SIGSEGV场景：
        - 访问空指针：*((int*)nullptr) = 5;
        - 访问未映射内存：*((int*)0x12345678) = 5;
        - 栈溢出：无限递归
        - 权限违规：写入只读内存
        
        SIGABRT场景：
        - 断言失败：assert(false);
        - 显式调用：abort();
        - 内存分配器错误：双重释放
        - 库函数检测到错误：Qt内存管理错误
        */
        
        demonstrateSignalDifferences();
    }
    
private:
    static void demonstrateSignalDifferences() {
        std::cout << "Signal comparison:" << std::endl;
        
        // SIGSEGV示例（注释掉以避免实际崩溃）
        std::cout << "SIGSEGV would be caused by:" << std::endl;
        std::cout << "  int* p = nullptr;" << std::endl;
        std::cout << "  *p = 42;  // <- SIGSEGV here" << std::endl;
        
        // SIGABRT示例
        std::cout << "\nSIGABRT would be caused by:" << std::endl;
        std::cout << "  if (error_detected) {" << std::endl;
        std::cout << "    abort();  // <- SIGABRT here" << std::endl;
        std::cout << "  }" << std::endl;
        
        // 在我们的案例中
        std::cout << "\nIn our case:" << std::endl;
        std::cout << "  QString::~QString() detects memory corruption" << std::endl;
        std::cout << "  -> QTypedArrayData::deallocate() calls free()" << std::endl;
        std::cout << "  -> free() detects double-free or corruption" << std::endl;
        std::cout << "  -> Memory allocator calls abort()" << std::endl;
        std::cout << "  -> abort() raises SIGABRT" << std::endl;
    }
};

// 5. 为什么这种设计是好的
class AbortDesignBenefits {
public:
    static void explainBenefits() {
        /*
        使用abort()而不是让程序继续运行的好处：
        
        1. 快速失败原则 (Fail Fast)
           - 一旦检测到错误立即停止
           - 避免错误传播和数据损坏
           - 便于问题定位
        
        2. 内存安全
           - 防止缓冲区溢出攻击
           - 避免use-after-free漏洞
           - 保护系统稳定性
        
        3. 调试友好
           - 生成core dump
           - 保留完整的调用栈
           - 提供明确的错误位置
        
        4. 可预测性
           - 错误行为是确定的
           - 不会出现随机崩溃
           - 便于测试和验证
        */
        
        std::cout << "Benefits of abort() over SIGSEGV:" << std::endl;
        std::cout << "1. Controlled termination with clear error signal" << std::endl;
        std::cout << "2. Complete stack trace preservation" << std::endl;
        std::cout << "3. Prevention of further memory corruption" << std::endl;
        std::cout << "4. Consistent and predictable error behavior" << std::endl;
        std::cout << "5. Security: prevents exploitation of memory errors" << std::endl;
    }
};

/*
总结：为什么是SIGABRT而不是SIGSEGV

1. 检测机制：
   - 程序（Qt和系统库）主动检测到内存管理错误
   - 不是被动的内存访问违规

2. 错误类型：
   - 双重释放或堆损坏
   - 不是简单的空指针访问

3. 安全策略：
   - 现代系统采用主动错误检测
   - 快速失败比继续运行更安全

4. 调用路径：
   QString::~QString() 
   -> QTypedArrayData::deallocate() 
   -> free() 
   -> 内存分配器检测到错误 
   -> abort() 
   -> raise(SIGABRT)

5. 设计优势：
   - 提供明确的错误信息
   - 保护系统不受进一步损害
   - 便于调试和问题定位

这种设计体现了现代软件的防御性编程思想，
优先考虑安全性和可调试性。
*/
