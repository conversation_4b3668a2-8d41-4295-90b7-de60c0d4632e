// 内存安全管理指南
// 针对海思Hi3536存储系统的内存管理最佳实践

#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <atomic>
#include <QSharedPointer>
#include <QWeakPointer>

// 1. 智能指针替代原始指针
class NetworkMessageManager {
private:
    // 使用智能指针管理消息数据
    using MessagePtr = std::shared_ptr<std::vector<char>>;
    using MessageQueue = std::vector<MessagePtr>;
    
    std::mutex m_queueMutex;
    MessageQueue m_messageQueue;
    std::atomic<bool> m_running{true};

public:
    // 安全的消息创建
    MessagePtr createMessage(const char* data, size_t len) {
        if (!data || len == 0 || len > 1024*1024) {  // 1MB限制
            return nullptr;
        }
        
        try {
            auto message = std::make_shared<std::vector<char>>();
            message->reserve(len);
            message->assign(data, data + len);
            return message;
        } catch (const std::bad_alloc& e) {
            qCritical("Memory allocation failed: %s", e.what());
            return nullptr;
        }
    }
    
    // 线程安全的消息队列操作
    bool enqueueMessage(MessagePtr message) {
        if (!message || !m_running) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_queueMutex);
        
        // 限制队列大小，防止内存泄漏
        if (m_messageQueue.size() >= 1000) {
            qWarning("Message queue full, dropping oldest message");
            m_messageQueue.erase(m_messageQueue.begin());
        }
        
        m_messageQueue.push_back(message);
        return true;
    }
    
    MessagePtr dequeueMessage() {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        
        if (m_messageQueue.empty()) {
            return nullptr;
        }
        
        auto message = m_messageQueue.front();
        m_messageQueue.erase(m_messageQueue.begin());
        return message;
    }
    
    void shutdown() {
        m_running = false;
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_messageQueue.clear();
    }
};

// 2. RAII资源管理
class NetworkCallbackGuard {
private:
    void* m_handle;
    bool m_valid;
    
public:
    explicit NetworkCallbackGuard(void* handle) 
        : m_handle(handle), m_valid(handle != nullptr) {
        if (m_valid) {
            // 获取资源锁或增加引用计数
        }
    }
    
    ~NetworkCallbackGuard() {
        if (m_valid) {
            // 自动释放资源
            m_handle = nullptr;
        }
    }
    
    // 禁止拷贝，只允许移动
    NetworkCallbackGuard(const NetworkCallbackGuard&) = delete;
    NetworkCallbackGuard& operator=(const NetworkCallbackGuard&) = delete;
    
    NetworkCallbackGuard(NetworkCallbackGuard&& other) noexcept 
        : m_handle(other.m_handle), m_valid(other.m_valid) {
        other.m_valid = false;
    }
    
    bool isValid() const { return m_valid; }
    void* getHandle() const { return m_valid ? m_handle : nullptr; }
};

// 3. 异常安全的字符串处理
class SafeStringConverter {
public:
    static QString convertSafely(const char* data, size_t len) {
        if (!data || len == 0) {
            return QString();
        }
        
        // 限制最大长度
        const size_t maxLen = 64 * 1024;  // 64KB
        if (len > maxLen) {
            qWarning("String too long (%zu bytes), truncating to %zu", len, maxLen);
            len = maxLen;
        }
        
        try {
            // 创建临时缓冲区，确保null终止
            std::vector<char> buffer(len + 1, '\0');
            std::memcpy(buffer.data(), data, len);
            
            // 尝试UTF-8转换
            QByteArray byteArray(buffer.data(), static_cast<int>(len));
            
            if (byteArray.isValidUtf8()) {
                return QString::fromUtf8(byteArray);
            } else {
                qWarning("Invalid UTF-8 sequence, using Latin-1");
                return QString::fromLatin1(byteArray);
            }
            
        } catch (const std::exception& e) {
            qCritical("String conversion failed: %s", e.what());
            return QString();
        }
    }
    
    // 安全的字符串比较
    static bool safeCompare(const QString& str1, const QString& str2) {
        try {
            return str1.compare(str2, Qt::CaseInsensitive) == 0;
        } catch (...) {
            return false;
        }
    }
};

// 4. 内存池管理（针对频繁分配的小对象）
template<typename T, size_t PoolSize = 1024>
class ObjectPool {
private:
    std::vector<std::unique_ptr<T>> m_pool;
    std::vector<T*> m_available;
    std::mutex m_mutex;
    
public:
    ObjectPool() {
        m_pool.reserve(PoolSize);
        m_available.reserve(PoolSize);
        
        // 预分配对象
        for (size_t i = 0; i < PoolSize; ++i) {
            auto obj = std::make_unique<T>();
            m_available.push_back(obj.get());
            m_pool.push_back(std::move(obj));
        }
    }
    
    T* acquire() {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_available.empty()) {
            qWarning("Object pool exhausted");
            return nullptr;
        }
        
        T* obj = m_available.back();
        m_available.pop_back();
        return obj;
    }
    
    void release(T* obj) {
        if (!obj) return;
        
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 重置对象状态
        obj->reset();
        m_available.push_back(obj);
    }
};

// 5. 内存泄漏检测辅助类
class MemoryTracker {
private:
    static std::atomic<size_t> s_allocCount;
    static std::atomic<size_t> s_deallocCount;
    static std::mutex s_trackMutex;
    static std::map<void*, size_t> s_allocations;
    
public:
    static void trackAllocation(void* ptr, size_t size) {
        s_allocCount++;
        
        std::lock_guard<std::mutex> lock(s_trackMutex);
        s_allocations[ptr] = size;
    }
    
    static void trackDeallocation(void* ptr) {
        s_deallocCount++;
        
        std::lock_guard<std::mutex> lock(s_trackMutex);
        s_allocations.erase(ptr);
    }
    
    static void printStats() {
        size_t allocs = s_allocCount.load();
        size_t deallocs = s_deallocCount.load();
        
        qInfo("Memory Stats - Allocs: %zu, Deallocs: %zu, Leaked: %zu", 
              allocs, deallocs, allocs - deallocs);
        
        std::lock_guard<std::mutex> lock(s_trackMutex);
        if (!s_allocations.empty()) {
            qWarning("Potential memory leaks detected: %zu allocations", 
                     s_allocations.size());
        }
    }
};

// 静态成员定义
std::atomic<size_t> MemoryTracker::s_allocCount{0};
std::atomic<size_t> MemoryTracker::s_deallocCount{0};
std::mutex MemoryTracker::s_trackMutex;
std::map<void*, size_t> MemoryTracker::s_allocations;

// 6. 使用示例：改进的回调函数
void improved_dev_msg_cb_v2(void* net_handle, int msg_code, char* buf, int len, void* context) {
    // RAII资源管理
    NetworkCallbackGuard guard(net_handle);
    if (!guard.isValid()) {
        return;
    }
    
    // 安全的消息管理
    static NetworkMessageManager messageManager;
    auto message = messageManager.createMessage(buf, len);
    
    if (!message) {
        qWarning("Failed to create message");
        return;
    }
    
    // 安全的字符串转换
    QString text = SafeStringConverter::convertSafely(
        message->data(), message->size());
    
    if (text.isEmpty()) {
        qWarning("Failed to convert message to string");
        return;
    }
    
    // 线程安全的处理
    MainWindow* mainWindow = static_cast<MainWindow*>(context);
    if (mainWindow) {
        QMetaObject::invokeMethod(
            mainWindow,
            [mainWindow, text, msg_code]() {
                mainWindow->processNetworkMessage(text, msg_code);
            },
            Qt::QueuedConnection
        );
    }
}
