// QTypedArrayData内存管理错误机制详细分析
// 解释为什么deallocate()会触发abort()

#include <QtCore/qarraydata.h>
#include <QtCore/qstring.h>
#include <cstdlib>
#include <iostream>

/*
Qt5.14.2中QString的内存管理结构：

QString内部使用QTypedArrayData<ushort>来存储UTF-16字符数据
每个QTypedArrayData对象包含：
- ref: 引用计数 (QAtomicInt)
- size: 数据大小
- alloc: 分配的容量
- capacityReserved: 容量保留标志
- offset: 数据偏移量
- data(): 实际字符数据的指针
*/

// 1. QTypedArrayData的内存布局
struct QArrayDataLayout {
    QAtomicInt ref;          // 引用计数
    int size;                // 当前大小
    uint alloc : 31;         // 分配的容量
    uint capacityReserved : 1; // 容量保留标志
    qptrdiff offset;         // 数据偏移量
    
    // 数据紧跟在结构体后面
    // [QArrayDataLayout][actual_data_bytes]
};

// 2. QString析构过程详解
class QStringDestructionAnalysis {
public:
    static void explainDestructionProcess() {
        /*
        QString析构过程：
        
        1. QString::~QString() 被调用
        2. 检查 d->ref.deref() - 减少引用计数
        3. 如果引用计数变为0，调用 QTypedArrayData<ushort>::deallocate(d)
        4. deallocate() 执行内存释放前的安全检查
        5. 如果检查失败，调用 abort()
        */
    }
    
    // 模拟QString的引用计数机制
    static void simulateRefCounting() {
        /*
        正常情况：
        QString s1 = "hello";     // ref = 1
        QString s2 = s1;          // ref = 2 (共享数据)
        s1.~QString();            // ref = 1 (减少引用计数)
        s2.~QString();            // ref = 0 (释放内存)
        
        异常情况：
        QString s1 = "hello";     // ref = 1
        // 内存被意外修改，ref变成无效值
        s1.~QString();            // ref.deref()返回异常值 -> abort()
        */
    }
};

// 3. deallocate()函数的安全检查机制
class DeallocateAnalysis {
public:
    // Qt源码中deallocate的简化版本
    static void deallocate_simplified(QArrayData* data) {
        if (!data) return;
        
        // 检查1：数据指针有效性
        if (data < (QArrayData*)0x1000) {  // 检查是否为空指针或很小的地址
            std::abort();  // 无效指针
        }
        
        // 检查2：引用计数合理性
        if (data->ref.load() < 0 || data->ref.load() > 1000000) {
            std::abort();  // 引用计数异常
        }
        
        // 检查3：内存对齐检查
        if ((uintptr_t)data % sizeof(void*) != 0) {
            std::abort();  // 内存未对齐
        }
        
        // 检查4：堆内存标记检查
        // Qt可能在调试模式下添加魔数检查
        uint32_t* magic = (uint32_t*)((char*)data - sizeof(uint32_t));
        if (*magic != 0xDEADBEEF) {  // 假设的魔数
            std::abort();  // 内存损坏或双重释放
        }
        
        // 检查5：分配器一致性检查
        // 确保内存是从正确的分配器分配的
        if (!isValidHeapPointer(data)) {
            std::abort();  // 不是有效的堆指针
        }
        
        // 如果所有检查通过，执行实际的内存释放
        free(data);
    }
    
private:
    static bool isValidHeapPointer(void* ptr) {
        // 简化的堆指针验证
        // 实际实现会检查指针是否在有效的堆范围内
        return ptr != nullptr && (uintptr_t)ptr > 0x1000;
    }
};

// 4. 具体的错误场景分析
class ErrorScenarios {
public:
    // 场景1：双重释放
    static void doubleFreeBug() {
        /*
        问题：同一个QArrayData被释放两次
        
        原因：
        1. 多线程环境下，两个线程同时调用QString析构
        2. 引用计数管理错误
        3. 手动内存管理错误
        
        检测：deallocate()发现内存已被释放（魔数被破坏）
        结果：abort()
        */
        
        QString* str1 = new QString("test");
        QString* str2 = str1;  // 浅拷贝，共享数据
        
        delete str1;  // 第一次释放
        // str2->d 现在指向已释放的内存
        delete str2;  // 第二次释放 -> abort()
    }
    
    // 场景2：野指针访问
    static void danglingPointerBug() {
        /*
        问题：QString的d指针指向已释放的内存
        
        原因：
        1. 缓冲区溢出覆盖了QString对象
        2. 栈损坏
        3. 内存管理错误
        
        检测：deallocate()发现指针无效
        结果：abort()
        */
        
        QString str = "test";
        // 假设某种内存损坏修改了str.d指针
        // str.d = (QStringData*)0x12345678;  // 无效地址
        // str析构时 -> abort()
    }
    
    // 场景3：引用计数损坏
    static void refCountCorruption() {
        /*
        问题：QArrayData的引用计数被意外修改
        
        原因：
        1. 缓冲区溢出
        2. 多线程竞争条件
        3. 内存踩踏
        
        检测：deallocate()发现引用计数异常
        结果：abort()
        */
        
        QString str = "test";
        // 假设内存损坏修改了引用计数
        // str.d->ref = -1;  // 无效的引用计数
        // str析构时 -> abort()
    }
    
    // 场景4：堆损坏
    static void heapCorruption() {
        /*
        问题：堆内存结构被破坏
        
        原因：
        1. 缓冲区溢出写入堆元数据
        2. 使用已释放的内存
        3. 内存分配器内部错误
        
        检测：deallocate()或malloc/free检测到堆损坏
        结果：abort()
        */
        
        QString str = "test";
        // 假设堆损坏
        // 当str析构时，堆分配器检测到损坏 -> abort()
    }
};

// 5. 网络回调中的具体错误机制
class NetworkCallbackErrors {
public:
    // 分析dev_msg_cb中可能的错误
    static void analyzeNetworkCallbackError() {
        /*
        基于GDB堆栈的分析：
        
        #7 MainWindow::dev_msg_cb(..., buf=0x7f68180bcc, len=256, ...)
        #6 QString::~QString (this=0x7f805f87d0, ...)
        #5 QTypedArrayData<unsigned short>::deallocate (data=0x7f60002f90)
        
        分析：
        1. QString对象地址：0x7f805f87d0 (栈上)
        2. QTypedArrayData地址：0x7f60002f90 (堆上)
        3. 网络缓冲区：0x7f68180bcc, 长度256
        
        可能的错误序列：
        */
        
        // 第135行可能的代码：
        char* buf = (char*)0x7f68180bcc;  // 网络缓冲区
        int len = 256;
        
        // 错误1：缓冲区内容被其他线程修改
        QString message = QString::fromUtf8(buf, len);
        // 在QString构造过程中，buf的内容被网络线程修改
        // 导致QString内部数据结构不一致
        
        // 错误2：len参数错误
        // len=256，但buf实际只有100字节有效数据
        // QString::fromUtf8读取了无效内存
        
        // 错误3：多线程引用计数竞争
        // 网络线程和UI线程同时操作QString
        // 引用计数管理出现竞争条件
        
        // 当函数结束时，message析构：
        // 1. QString::~QString()检查引用计数
        // 2. 发现引用计数异常或数据损坏
        // 3. QTypedArrayData::deallocate()执行安全检查
        // 4. 检查失败，调用abort()
    }
    
    // 重现错误的测试代码
    static void reproduceError() {
        // 模拟网络缓冲区
        char network_buffer[256];
        memset(network_buffer, 'A', 255);
        network_buffer[255] = '\0';
        
        // 模拟多线程访问
        std::thread network_thread([&]() {
            while (true) {
                // 网络线程不断修改缓冲区
                memset(network_buffer, rand() % 256, 256);
                std::this_thread::sleep_for(std::chrono::microseconds(1));
            }
        });
        
        std::thread ui_thread([&]() {
            for (int i = 0; i < 1000; ++i) {
                try {
                    // UI线程尝试创建QString
                    QString message = QString::fromUtf8(network_buffer, 256);
                    // message析构时可能崩溃
                } catch (...) {
                    // 捕获异常
                }
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
        network_thread.detach();
        ui_thread.join();
    }
};

/*
总结：QTypedArrayData::deallocate()触发abort()的原因

1. 双重释放检测
   - 内存已被释放，但再次尝试释放
   - 堆分配器检测到重复释放

2. 野指针检测
   - QString.d指向无效内存地址
   - 地址范围检查失败

3. 引用计数异常
   - ref计数为负数或异常大的值
   - 表明内存损坏或竞争条件

4. 内存对齐错误
   - 指针未按要求对齐
   - 表明内存损坏

5. 堆完整性检查
   - 堆元数据被破坏
   - 魔数检查失败

在网络回调场景中，最可能的原因是多线程竞争导致的
引用计数错误或缓冲区内容在QString构造过程中被修改。
*/
