// 递归清理空目录功能的使用示例和说明
// 展示如何使用新增的cleanupEmptyDirectoriesRecursively功能

#include "disk_manager.h"
#include <iostream>

/*
递归清理空目录功能说明：

1. 功能目的：
   - 防止磁盘清理后留下大量空的目录结构
   - 保持文件系统的整洁
   - 自动向上清理只包含index.db的目录

2. 工作流程：
   - 当cleanupDirectoryByFiles成功删除一个目录后
   - 自动检查其父目录是否也变成了空目录或只包含index.db
   - 如果是，则删除index.db文件并删除父目录
   - 继续向上递归，直到遇到包含其他文件的目录或到达根目录

3. 安全机制：
   - 严格的路径检查，确保不会删除根目录
   - 只在指定的cleanup_path范围内操作
   - 详细的日志记录所有删除操作
   - 权限检查和错误处理

4. 使用场景示例：
   假设有如下目录结构：
   /storage/2024-01-15/1/
   /storage/2024-01-15/1/video1.mp4  (被删除)
   /storage/2024-01-15/1/video2.mp4  (被删除)
   /storage/2024-01-15/index.db
   /storage/index.db

   清理过程：
   1. 删除/storage/2024-01-15/1/目录下的所有视频文件
   2. 删除空的/storage/2024-01-15/1/目录
   3. 检查/storage/2024-01-15/目录，发现只有index.db
   4. 删除/storage/2024-01-15/index.db
   5. 删除空的/storage/2024-01-15/目录
   6. 检查/storage/目录，发现还有其他文件，停止清理
*/

void demonstrateRecursiveCleanup() {
    std::cout << "=== 递归清理空目录功能演示 ===" << std::endl;
    
    // 创建DiskManager实例
    DiskManager diskManager;
    
    // 示例：清理某个通道的录像文件
    std::string storage_path = "/opt/storage";
    std::string channel_path = "/opt/storage/2024-01-15/1";
    
    std::cout << "1. 开始清理通道目录: " << channel_path << std::endl;
    
    // 模拟清理过程
    uint64_t freed_space = 0;
    uint64_t target_space = 1024 * 1024 * 1024; // 1GB
    
    // 调用cleanupDirectoryByFiles会自动触发递归清理
    bool success = diskManager.cleanupDirectoryByFiles(
        channel_path, 
        freed_space, 
        target_space, 
        storage_path
    );
    
    if (success) {
        std::cout << "2. 清理成功，已释放空间: " << freed_space / (1024*1024) << " MB" << std::endl;
        std::cout << "3. 递归清理已自动执行，清理了所有空目录" << std::endl;
    } else {
        std::cout << "2. 清理未完全达到目标" << std::endl;
    }
    
    std::cout << "\n=== 清理过程说明 ===" << std::endl;
    std::cout << "- 删除目录中的所有文件" << std::endl;
    std::cout << "- 删除空目录" << std::endl;
    std::cout << "- 检查父目录是否只包含index.db" << std::endl;
    std::cout << "- 如果是，删除index.db并删除父目录" << std::endl;
    std::cout << "- 继续向上递归，直到遇到非空目录" << std::endl;
}

// 安全检查功能说明
void explainSafetyFeatures() {
    std::cout << "\n=== 安全特性说明 ===" << std::endl;
    
    std::cout << "1. 路径安全检查：" << std::endl;
    std::cout << "   - 不会删除根目录 (/)" << std::endl;
    std::cout << "   - 不会删除指定的root_path" << std::endl;
    std::cout << "   - 只在root_path范围内操作" << std::endl;
    
    std::cout << "\n2. 文件检查：" << std::endl;
    std::cout << "   - 只删除确认为index.db的文件" << std::endl;
    std::cout << "   - 发现其他文件时停止清理" << std::endl;
    std::cout << "   - 详细的文件类型检查" << std::endl;
    
    std::cout << "\n3. 错误处理：" << std::endl;
    std::cout << "   - 权限检查和错误日志" << std::endl;
    std::cout << "   - 删除失败时停止递归" << std::endl;
    std::cout << "   - 详细的操作日志记录" << std::endl;
    
    std::cout << "\n4. 递归控制：" << std::endl;
    std::cout << "   - 明确的终止条件" << std::endl;
    std::cout << "   - 防止无限递归" << std::endl;
    std::cout << "   - 路径长度和深度检查" << std::endl;
}

// 日志输出示例
void showLogExamples() {
    std::cout << "\n=== 日志输出示例 ===" << std::endl;
    
    std::cout << "正常清理过程的日志：" << std::endl;
    std::cout << "[INFO] 删除空目录成功: /opt/storage/2024-01-15/1" << std::endl;
    std::cout << "[INFO] 删除index.db文件: /opt/storage/2024-01-15/index.db" << std::endl;
    std::cout << "[INFO] 递归删除空目录: /opt/storage/2024-01-15" << std::endl;
    std::cout << "[INFO] 父目录包含其他文件，停止递归清理: /opt/storage" << std::endl;
    
    std::cout << "\n错误情况的日志：" << std::endl;
    std::cout << "[ERROR] 删除index.db文件失败: /path/index.db, errno=13" << std::endl;
    std::cout << "[ERROR] 递归删除目录失败: /path/dir, errno=39" << std::endl;
    std::cout << "[WARN] 目录路径超出根路径范围，停止清理: /invalid/path" << std::endl;
}

int main() {
    demonstrateRecursiveCleanup();
    explainSafetyFeatures();
    showLogExamples();
    
    return 0;
}

/*
总结：

这个递归清理功能的实现遵循了KISS原则：
1. 只在现有代码中添加了必要的逻辑
2. 复用了现有的目录操作和日志系统
3. 保持了代码的简洁性和可读性
4. 添加了充分的安全检查和错误处理

主要改动：
1. 在disk_manager.h中添加了一个私有方法声明
2. 在disk_manager.cpp中实现了递归清理逻辑
3. 在cleanupDirectoryByFiles中添加了一行调用

这个实现确保了：
- 磁盘清理后不会留下空的目录结构
- 安全地处理index.db文件
- 提供详细的操作日志
- 具有完善的错误处理和安全检查
*/
