#pragma once

#include <string>
#include <vector>
#include <stdint.h>
#include "Thread/Lock.hpp"
#include "Thread/Cond.hpp"
#include <map>
#include <pthread.h>
#include <atomic>
#include <functional>
#include <queue>

// ========== 统一磁盘空间阈值定义 ==========

#define DISK_CRITICAL_THRESHOLD_PERCENT 96.0    // 磁盘使用率超过95%为严重状态
#define DISK_CLEANUP_THRESHOLD_PERCENT  92.0    // 磁盘使用率超过93%触发清盘
#define DISK_WARNING_THRESHOLD_PERCENT  88.0    // 磁盘使用率超过90%发出警告
#define DISK_SWITCH_THRESHOLD_PERCENT   85.0    // 磁盘使用率超过90%触发磁盘切换
#define	DISK_STOP_THRESHOLD_PERCENT		80.0	// 磁盘使用率小于85%停止清盘

#define DISK_CRITICAL_WAIT				10		// 严重不足等待时间
#define DISK_CLEANUP_WAIT				20		// 标准清理间隔时间
#define DISK_WARNING_WAIT				300		// 警告间隔时间

// 清盘相关配置
#define DISK_CLEANUP_CACHE_TIMEOUT_SECONDS 5   	// 磁盘状态缓存超时时间（秒）
#define	DISK_CLEANUP_INTERVAL_MINUTES	   3	// 磁盘清理间隔时间 (分钟)
// ========== 代码分离重构：类型定义和新增类的完整定义 ==========

// 磁盘状态结构体（移动到前面以供新增类使用）
typedef struct {
    std::string path;        // 磁盘路径
    std::string device;      // 设备路径 (如/dev/sda1)
    std::string uuid;        // 磁盘UUID
    std::string fsType;      // 文件系统类型
    bool        isMounted;   // 是否已挂载
    bool        isRemovable; // 是否可移动设备
    uint64_t    totalSpace;  // 总空间 (字节)
    uint64_t    availSpace;  // 可用空间 (字节)
    time_t      lastChecked; // 最后检查时间
    int         status;      // 状态 (0:正常,1:空间不足,2:不可用,3:错误)
    std::string statusMessage; // 状态描述
} DiskInfo;

// 挂载设备信息结构
struct MountInfo {
    std::string device;          // 设备路径 (/dev/sda1)
    std::string mountPoint;      // 挂载点 (/mnt/disk1)
    std::string fsType;          // 文件系统类型 (ext4, ntfs等)
    std::string uuid;            // 设备UUID
    time_t mountTime;            // 挂载时间
    bool isRemovable;            // 是否可移动设备
    bool isUsb;                  // 是否USB设备
    uint64_t totalSpace;         // 总空间
    uint64_t availableSpace;     // 可用空间
    time_t lastChecked;          // 最后检查时间

    MountInfo();
    MountInfo(const std::string& dev, const std::string& mount, const std::string& fs);
};

// 挂载设备缓存管理器类定义
class MountedDeviceCache {
public:
    MountedDeviceCache();
    ~MountedDeviceCache();

    // 核心缓存操作
    void addMountedDevice(const std::string& device, const MountInfo& info);
    void removeMountedDevice(const std::string& device);
    bool isDeviceMounted(const std::string& device) const;
    std::string getCacheDeviceMountPoint(const std::string& device) const;
    std::vector<MountInfo> getAllMountedDevices() const;
    void refreshCache();
    bool needsRefresh() const;

private:
    mutable TCSLock m_cacheMutex;                        // 缓存访问锁
    std::map<std::string, MountInfo> m_mountedDevices;   // 已挂载设备缓存
    std::atomic<time_t> m_lastRefreshTime;               // 最后刷新时间
    std::atomic<bool> m_refreshInProgress;               // 刷新进行中标志

    // 内部方法
    std::map<std::string, MountInfo> getSystemMountInfo() const;
};

// USB设备独立管理器类定义
class UsbDeviceManager {
public:
    UsbDeviceManager(MountedDeviceCache* mountCache);
    ~UsbDeviceManager();

    // USB检测控制
    bool startDetection();
    void stopDetection();

    // USB设备操作
    std::vector<DiskInfo> getUsbDevices() const;
    bool mountUsbDevice(const std::string& device, const std::string& mountPoint = "");
    bool unmountUsbDevice(const std::string& device);
    void updateUsbDeviceStatus();

    // USB设备查询
    bool isUsbDeviceMounted(const std::string& device) const;
    std::string getUsbDeviceMountPoint(const std::string& device) const;

private:
    mutable TCSLock m_usbManagerLock;                    // USB管理器专用锁
    std::atomic<bool> m_detectionRunning;                // 检测线程运行状态
    pthread_t m_detectionThread;                         // 检测线程
    std::vector<DiskInfo> m_usbDevices;                  // USB设备列表
    std::map<std::string, std::string> m_usbMountPoints; // USB挂载点映射
    int m_checkIntervalSeconds;                          // 检测间隔
    MountedDeviceCache* m_mountCache;                    // 挂载缓存引用

    // 内部方法
    static void* usbDetectionThreadFunc(void* arg);
    void usbDetectionWork();
    std::vector<std::string> scanUsbDevices() const;
    DiskInfo getUsbDeviceInfo(const std::string& device) const;
    std::string generateUsbMountPoint(const std::string& device) const;
    std::string detectFilesystemType(const std::string& device) const;
    bool isUsbDevice(const std::string& device) const;
};

// 确保 pthread_timedjoin_np 可用
#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#include <pthread.h>
#include <errno.h>

// 声明全局变量，用于标记磁盘是否正在格式化中
extern std::atomic<bool> g_disk_formatting;



// DiskInfo 已移动到文件前面，避免重复定义

// 过度设计的磁盘管理策略枚举已删除

// 最多7个硬盘
#define MAX_DISK_COUNT  7

// 挂载信息结构
typedef struct {
    char *device;     // 设备路径，例如 /dev/sda1
    char *mountpoint; // 挂载路径，例如 /mnt/custom/disk/disk1
} MOUNT_INFO;

// 定义磁盘顶层目录
#define DISK_MOUNT_BASE "/mnt/custom/disk/"

// 移动磁盘挂载路径
#define USB_MOUNT_BASE "/mnt/custom/usb"

// 定义挂载映射关系
#define DISK_MAPPING { \
	{ (char *)"/dev/sda1", (char *)DISK_MOUNT_BASE "disk1" }, \
    { (char *)"/dev/sdb1", (char *)DISK_MOUNT_BASE "disk2" }, \
    { (char *)"/dev/sdc1", (char *)DISK_MOUNT_BASE "disk3" }, \
    { (char *)"/dev/sdd1", (char *)DISK_MOUNT_BASE "disk4" }, \
    { (char *)"/dev/sde1", (char *)DISK_MOUNT_BASE "disk5" }, \
    { (char *)"/dev/sdf1", (char *)DISK_MOUNT_BASE "disk6" }, \
	{ (char *)"/dev/sdg1", (char *)DISK_MOUNT_BASE "disk7" }  \
}

// 注意：磁盘阈值定义已统一到文件开头，这里的旧定义已删除

// UUID映射文件路径
#define UUID_MAPPING_FILE 		"/mnt/custom/config/Qt/db/disk_uuid_mapping.conf"

// 磁盘切换回调函数类型
typedef std::function<void(const std::string&, const std::string&)> DiskSwitchCallback;

// UUID映射结构
// UuidMapping结构体已移动到外部UUIDManager类中

// UUID映射数据结构
typedef struct {
    std::string uuid;
    std::string mountPoint;
    std::string label;
    time_t lastMounted;
} UuidMapping;

// UUID管理器外部类定义（与UsbDeviceManager保持一致）
class UUIDManager {
public:
    UUIDManager();
    ~UUIDManager();

    // UUID映射管理
    void addUUIDMapping(const std::string& uuid, const std::string& mountPoint);
    bool removeUUIDMapping(const std::string& uuid);
    bool removeUUIDMappingByDevice(const std::string& devicePath);

    // UUID文件操作
    bool loadUUIDMappings();
    bool saveUUIDMappings();

    // UUID查询功能
    std::string getDiskUUID(const std::string& device) const;
    std::string getMountPointByUUID(const std::string& uuid) const;
    std::string getNextAvailableMountPoint() const;

    // UUID验证功能
    bool validateUUIDConsistency(const std::string& devicePath) const;
    void cleanupDuplicateUUIDs(const std::string& uuid);

private:
    std::vector<UuidMapping> m_uuidMappings;  // UUID映射列表
    mutable TCSLock m_uuidMutex;              // UUID操作锁
    time_t m_lastLoadTime;                    // 最后加载时间
};

// 磁盘管理类（单例模式）
class DiskManager {
public:
    // 获取单例实例
    static DiskManager* getInstance();

    // 销毁单例实例
    static void destroyInstance();

    // 初始化磁盘管理（只能在主程序启动时调用一次）
    bool initializeOnStartup();

    // 检查模块是否已初始化
    bool isModuleInitialized() const { return m_moduleInitialized; }

    // 磁盘路径管理方法（供初始化使用）
    void clearDiskPaths();
    void addDiskPath(const std::string& path);
    void setActiveDiskPathInternal(const std::string& path);
    void setModuleInitialized(bool initialized);

    // 开始和停止检查
    void startChecking();
    void stopChecking();

    // === 优化后的非阻塞接口 ===

    // 获取活动磁盘路径（非阻塞，从缓存返回）
    std::string getActiveDiskPath() const;

    // 设置活动磁盘路径（非阻塞，使用TryLock避免阻塞）
    bool setActiveDiskPath(const std::string& path);

    // 获取所有磁盘信息（非阻塞，从缓存返回，后台更新）
    std::vector<DiskInfo> getAllDisksInfo();

    // 获取指定磁盘信息（非阻塞，从缓存返回）
    DiskInfo getDiskInfo(const std::string& path);

    // 触发后台磁盘状态更新（非阻塞）
    void triggerDiskStatusUpdate();

    // === 日志控制接口 ===

    // 设置静默模式（禁用磁盘相关日志）
    void setSilentMode(bool enabled);

    // 检查是否为静默模式
    bool isSilentMode() const;

    // 更新所有磁盘状态
    void updateAllDiskStatus();

    // 检查指定路径是否已挂载
    bool isMounted(const std::string& path) const;

    // 检查设备是否已挂载到任何位置
    bool isDeviceAlreadyMounted(const std::string& devicePath) const;

    // 获取设备当前挂载点
    std::string getDeviceMountPoint(const std::string& devicePath) const;

    // 检查路径是否为USB设备路径
    bool isUsbDevicePath(const std::string& path) const;

    // ========== 统一磁盘空间检测接口 ==========

    // 获取磁盘使用百分比（统一接口，带缓存机制）
    double getDiskUsagePercent(const std::string& path, bool force_check = false);

    // 检查磁盘空间是否严重不足
    bool isCriticalSpace(const std::string& path);

    // 检查磁盘是否满了需要切换
    bool isDiskFull(const std::string& path, bool force_check = false);

    // 统一清盘触发检查（内部使用）
    void checkAndTriggerCleanupIfNeeded(const std::string& path, const char* trigger_source = "unknown");

    // 切换到下一个可用磁盘
    bool switchToNextDisk();

    // 切换到指定磁盘
    bool switchToDisk(const std::string& path);

    // 过度设计的磁盘管理接口已删除

    // 清理磁盘空间
    void cleanupSpace(uint64_t bytesToFree);

    // 按通道清理磁盘空间
    bool cleanupChannelSpace(int channel_id, uint64_t bytesToFree, const std::string& path = "");

    // 请求异步清盘
    void requestAsyncCleanup(int channel_id, uint64_t bytesToFree);

    // 内部挂载方法（供初始化和外部调用使用）
    bool mountDiskInternal(const std::string& device);

    // 清理状态检查方法
    bool isCleanupInProgress() const;
    bool waitForCleanupComplete(int timeoutSeconds = 30);

    // 自动切换模式 - 检查是否需要切换磁盘
    bool checkAndSwitchDiskIfNeeded();

    // 主动触发磁盘切换检查（供录像模块调用）
    void triggerDiskSwitchCheck();

    // 录像文件完整性保护接口
    bool isRecordingInProgress() const;
    bool waitForRecordingComplete(int timeoutSeconds = 30);
    void notifyDiskSwitchPending();

    // 平滑过渡机制接口
    bool isDiskSwitchPending() const;
    void setPendingSwitchTarget(const std::string& targetDisk);
    std::string getPendingSwitchTarget() const;
    bool executePendingDiskSwitch();
    void clearPendingSwitch();
    std::string findNextAvailableDisk(const std::string& currentDisk);

    // ========== 磁盘空间不足紧急处理接口 ==========

    /**
     * 磁盘写入错误紧急处理（非阻塞，多通道并发优化）
     * 当创建文件失败时调用，支持多种错误码：
     * - errno=28 (No space left on device) - 磁盘空间不足
     * - errno=5 (Input/output error) - I/O错误
     * - errno=30 (Read-only file system) - 只读文件系统
     * - errno=122 (Disk quota exceeded) - 磁盘配额超限
     * - errno=27 (File too large) - 文件过大
     * - errno=74 (EBADMSG) - 文件系统错误
     *
     * @param failedPath 失败的文件路径
     * @param channelId 录像通道ID
     * @param requiredSpace 需要的最小空间(字节)，默认512MB
     * @return true 处理成功，可以重试创建文件; false 处理已启动，建议稍后重试
     */
    bool handleDiskSpaceEmergency(const std::string& failedPath, int channelId);

    
    /**
     * 紧急磁盘切换
     * 当清理无效时，立即切换到其他可用磁盘
     *
     * @param currentPath 当前磁盘路径
     * @param channelId 录像通道ID
     * @return true 切换成功; false 切换失败
     */
    bool emergencyDiskSwitch(const std::string& currentPath, int channelId);
	/**
     * 检查磁盘是否有足够空间创建文件
     *
     * @param diskPath 磁盘路径
     * @return true 空间足够; false 空间不足
     */
    bool hasEnoughSpaceForFile(const std::string& diskPath);

    /**
     * 检查是否有可用的已挂载磁盘（带缓存优化）
     *
     * @param forceCheck 是否强制检查，忽略缓存
     * @return true 有可用磁盘; false 无可用磁盘
     */
    bool hasAvailableDisk(bool forceCheck = false) const;

    /**
     * 🚀 优化：清除硬盘状态缓存（用于硬盘重新接入后的状态刷新）
     */
    void clearDiskStatusCache() const;

    /**
     * 获取UUID管理器实例
     *
     * @return UUID管理器指针
     */
    UUIDManager* getUUIDManager() const { return m_uuidManager; }

    /**
     * 获取USB设备管理器实例
     *
     * @return USB设备管理器指针
     */
    UsbDeviceManager* getUsbDeviceManager() const { return m_usbManager; }


	void getFirstPartition(const char *dev, char *out, size_t out_size);
	static int deleteFormatPartition(const char *dev_disk);
	int formatDisk(const char *disk);
	// 挂载分区 (开始启动时调用)
	int mountDisk(const char *device);
	// 检查设备是否存在
    bool isDeviceExists(const std::string& devicePath);  
	// 检查是否是移动设备
    bool isRemovableDevice(const std::string& devicePath);  


private:
    // 紧急处理辅助函数
    std::string extractDiskPathFromFile(const std::string& filePath);
    // 辅助方法：获取DISK_MAPPING信息
    std::string findDeviceByMountPath(const std::string& mountPath);			   // 根据挂载路径查找设备路径
    static std::vector<std::pair<std::string, std::string>> getAllDiskMappings();  // 获取所有映射关系
    static std::string convertToPartitionPath(const std::string& devicePath);  // 将磁盘路径转换为分区路径

private:
    DiskManager();  // 私有构造函数
    ~DiskManager(); // 私有析构函数

    // 禁用拷贝构造和赋值操作
    DiskManager(const DiskManager&) = delete;
    DiskManager& operator=(const DiskManager&) = delete;

    // 内部初始化实现
    bool initializeInternal();

    // 从DISK_MAPPING更新磁盘列表
    void updateDiskListFromMapping(const std::string& mountPoint);

    // 内部工具函数，用于规范化路径格式
    std::string normalizePath(const std::string& path) const {
        std::string result = path;
        // 确保路径末尾有斜杠，但仅用于文件路径操作，不用于挂载检查
        if (!result.empty() && result.back() != '/') {
            result += '/';
        }
        return result;
    }
    
    // 异步清盘线程函数和工作函数
    static void* cleanupThreadFunc(void* arg);
    void cleanupThreadWork();
    
    // 定期磁盘检查线程函数和工作函数
    static void* periodicCheckThreadFunc(void* arg);
    void periodicCheckWork();
    
    // USB检测线程已删除 - 使用独立的USB设备操作类
    
    // 清理目录中的文件
    bool cleanupDirectoryByFiles(const std::string& dir_path, uint64_t& freed_space,
                                uint64_t bytesToFree, const std::string& cleanup_path);

    /**
     * 尝试删除空目录（index.db文件被视为可忽略的系统文件）
     *
     * @param dir_path 要检查和删除的目录路径
     * @return bool 是否成功删除了目录
     *              true: 目录被成功删除
     *              false: 目录未被删除（不符合删除条件或删除失败）
     *
     * @note 此函数的空目录定义：
     *       - 完全空的目录（不包含任何文件和子目录）
     *       - 只包含index.db文件的目录（index.db被视为系统文件，可忽略）
     *
     * @note 此函数会：
     *       1. 检查目录内容，index.db文件不影响空目录判断
     *       2. 如果目录符合删除条件，先删除index.db文件（如果存在）
     *       3. 然后删除空目录
     *       4. 记录详细的操作日志
     *       5. 提供完整的错误处理
     */
    bool tryDeleteEmptyDirectory(const std::string& dir_path);
    
    // 定期检查线程
    void periodicDiskCheck();
    
    // 策略相关的路径获取方法已删除
    
    // 成员变量
    std::vector<std::string> m_diskPaths;            // 磁盘路径列表
    std::map<std::string, DiskInfo> m_diskInfoMap;   // 磁盘信息映射
    size_t m_activeDiskIndex;                        // 当前活动磁盘索引
    std::string m_activeDiskPath;                    // 当前活动磁盘路径
    bool m_running;                                  // 运行状态
    // 未使用的成员变量已删除：m_channelDiskMap, m_recordPathPolicy, m_switchCallback
    
    // 清盘相关变量
    std::atomic<bool> m_isCleaningUp;                // 是否正在清盘
    std::atomic<bool> m_cleanupRequested;            // 是否请求清盘
    int m_cleanupChannelId;                          // 请求清盘的通道ID
    uint64_t m_cleanupBytesToFree;                   // 请求释放的字节数
    pthread_t m_cleanupThread;                       // 清盘线程
    TCSLock m_cleanupMutex;                          // 清盘互斥锁
    TCondition m_cleanupCond;                        // 清盘条件变量

    // 定期检查相关变量
    pthread_t m_periodicCheckThread;                 // 定期检查线程
    int m_checkIntervalMinutes;                      // 检查间隔(分钟)

    // 线程状态统一管理（USB检测线程已删除）
    struct ThreadStates {
        std::atomic<bool> cleanup{false};            // 清理线程状态
        std::atomic<bool> periodicCheck{false};      // 定期检查线程状态
    } m_threadStates;

    // === 非阻塞操作支持 ===
    mutable TCSLock m_diskInfoLock;                  // 磁盘信息锁（使用TryLock避免阻塞）
    TCondition m_backgroundTaskCond;                 // 后台任务条件变量
    std::atomic<bool> m_backgroundUpdateRequested{false}; // 后台更新请求标志
    mutable std::atomic<bool> m_silentMode{false};   // 静默模式标志（mutable允许在const函数中修改）

    // 平滑过渡机制状态变量
    mutable std::atomic<bool> m_diskSwitchPending{false};    // 磁盘切换待处理标志
    mutable std::string m_pendingSwitchTarget;               // 待切换的目标磁盘路径
    mutable TCSLock m_pendingSwitchLock;                     // 待切换状态锁
    mutable time_t m_pendingSwitchSetTime{0};                // 延迟切换设置时间（用于超时检查）
    static const int PENDING_SWITCH_TIMEOUT_SECONDS = 30;   // 延迟切换超时时间（秒）

    // 🚀 优化：硬盘状态缓存机制（减少重复检测）
    struct DiskStatusCache {
        bool hasAvailableDisk;                               // 是否有可用磁盘
        time_t lastCheckTime;                                // 上次检查时间
        static const int CACHE_TIMEOUT = 5;                 // 缓存超时时间（秒）

        DiskStatusCache() : hasAvailableDisk(false), lastCheckTime(0) {}

        bool isValid() const {
            return (time(nullptr) - lastCheckTime) < CACHE_TIMEOUT;
        }
    };
    mutable DiskStatusCache m_diskStatusCache;               // 磁盘状态缓存
    mutable TCSLock m_diskStatusCacheLock;                   // 缓存保护锁

    // 后台任务处理方法（简化版本）
    bool unmountDiskInternal(const std::string& path);
    void updateAllDiskStatusInternal();
    // 复杂的后台任务队列已删除，使用直接调用方式
    // USB相关变量已删除 - 使用独立的USB设备操作类
    
    // 磁盘切换相关变量已简化删除

    // 录像完成检查和切换控制
    time_t m_lastCleanupTime;                        // 上次清理时间
    // 未使用的成员变量已删除：m_pendingDiskSwitch, m_pendingTargetDisk
    static const int CLEANUP_INTERVAL_HOURS = 2;    // 清理间隔（小时）

    // 录像完成检查方法
    bool isRecordingCompleteOnDisk(const std::string& diskPath);
    bool canSwitchToDisk(const std::string& targetDisk);
    std::string findRollbackDisk(); // 查找回滚目标磁盘

    // 单例相关
    static DiskManager* m_instance;
    static TCSLock m_instanceMutex;

    // 模块初始化标志，确保只在主程序启动时初始化一次
    bool m_moduleInitialized;

    // ========== 第一阶段新增成员变量 ==========

    // 缓存和管理器
    MountedDeviceCache* m_mountCache;                    // 挂载设备缓存
    UsbDeviceManager* m_usbManager;                      // USB设备管理器
    UUIDManager* m_uuidManager;                          // UUID管理器

    // 重复的原子状态变量已删除，统一使用 m_threadStates
};

// 挂载磁盘的外部接口
int vs_mount_disk(const char *device);

// 格式化磁盘的外部接口
int vs_format_disk(const char *disk);

// 更新所有磁盘状态的外部接口
void vs_update_all_disk_status();

// 设置静默模式的外部接口
void vs_set_disk_silent_mode(bool enabled);

// 检查是否有可用磁盘的外部接口
bool vs_has_available_disk();

// ========== 顶层初始化接口 ==========

/**
 * 磁盘管理模块顶层初始化函数
 * 供主程序启动时调用，其他模块不得调用
 *
 * @param diskPaths 磁盘路径列表
 * @return true 初始化成功, false 初始化失败
 */
bool disk_module_initialize();

/**
 * 磁盘管理模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void disk_module_cleanup();

/**
 * 检查磁盘管理模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool disk_module_is_initialized();

/**
 * 代码分离重构验证接口
 * 测试新增类的功能是否正常
 */
void vs_test_code_separation();

/**
 * 测试磁盘清盘功能
 * 验证磁盘空间不足时的自动清盘机制
 */
void vs_test_disk_cleanup();
void test_disk_auto_switch();




