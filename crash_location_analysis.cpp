// 崩溃位置详细分析
// 基于GDB堆栈信息推断MainWindow::dev_msg_cb第135行的具体操作

#include <QString>
#include <QByteArray>
#include <QDebug>

/*
GDB堆栈分析：
#7  MainWindow::dev_msg_cb (net_handle=0x44406a0, msg_code=2177, buf=0x7f68180bcc, len=256, context=0x4440470)
    at qt/mainwindow.cpp:135
#6  QString::~QString (this=0x7f805f87d0, __in_chrg=<optimized out>)
    at /opt/Qt5.14.2_mix410/include/QtCore/qstring.h:1263
#5  QTypedArrayData<unsigned short>::deallocate (data=0x7f60002f90)
    at /opt/Qt5.14.2_mix410/include/QtCore/qarraydata.h:239

关键信息：
- 函数参数：msg_code=2177, buf=0x7f68180bcc, len=256
- QString对象地址：0x7f805f87d0 (栈上对象)
- QTypedArrayData地址：0x7f60002f90 (堆上数据)
*/

// 1. 最可能的崩溃场景：临时QString对象析构
void problematic_dev_msg_cb_scenario1(void* net_handle, int msg_code, char* buf, int len, void* context) {
    // 第135行可能的代码：
    QString message = QString::fromUtf8(buf, len);  // 创建临时QString对象
    
    // 问题：buf指向的内存可能已经被释放或损坏
    // 当QString析构时，尝试释放内部的QTypedArrayData，但数据已损坏
    
    // 函数结束时，message对象析构 -> 触发QString::~QString()
    // -> 调用QTypedArrayData<unsigned short>::deallocate()
    // -> 检测到内存损坏 -> 调用abort()
}

// 2. 另一种可能：字符串拷贝操作
void problematic_dev_msg_cb_scenario2(void* net_handle, int msg_code, char* buf, int len, void* context) {
    MainWindow* window = static_cast<MainWindow*>(context);
    
    // 第135行可能的代码：
    QString text;
    if (len > 0) {
        text = QString::fromLocal8Bit(buf, len);  // 可能触发内存错误
    }
    
    // 或者是信号发射：
    emit window->messageReceived(text);  // text作为参数传递时可能出错
}

// 3. 最危险的场景：跨线程QString操作
void problematic_dev_msg_cb_scenario3(void* net_handle, int msg_code, char* buf, int len, void* context) {
    MainWindow* window = static_cast<MainWindow*>(context);
    
    // 第135行可能的代码：
    QString message = QString::fromUtf8(buf, len);
    
    // 危险操作：在网络线程中直接操作UI线程的QString成员
    window->m_lastMessage = message;  // 跨线程赋值，可能导致引用计数错误
    
    // 或者：
    window->updateStatusText(message);  // 跨线程调用UI更新函数
}

// 4. 内存边界问题
void problematic_dev_msg_cb_scenario4(void* net_handle, int msg_code, char* buf, int len, void* context) {
    // 第135行可能的代码：
    
    // 问题1：len参数不可信
    if (len > 1000000) {  // 异常大的长度值
        QString huge_string = QString::fromUtf8(buf, len);  // 尝试分配巨大内存
    }
    
    // 问题2：buf指针无效
    if (buf == nullptr || len < 0) {
        QString invalid_string = QString::fromUtf8(buf, len);  // 访问无效内存
    }
    
    // 问题3：缓冲区越界
    QString message = QString::fromUtf8(buf, len);  // len超过buf实际大小
}

/*
根据堆栈信息的详细分析：

1. 崩溃发生在QString析构函数中
   - QString对象地址：0x7f805f87d0 (看起来是栈上的局部变量)
   - 这表明是一个临时QString对象在函数结束时析构

2. QTypedArrayData地址：0x7f60002f90
   - 这是QString内部存储字符数据的堆内存
   - 地址看起来正常，但内容可能已损坏

3. 函数参数分析：
   - msg_code=2177：特定的消息类型
   - buf=0x7f68180bcc：网络数据缓冲区地址
   - len=256：数据长度（看起来合理）
   - context=0x4440470：MainWindow对象指针

最可能的情况是第135行创建了一个QString对象，
但由于某种原因（内存损坏、多线程冲突、缓冲区问题），
当函数结束时QString析构失败。
*/

// 重现问题的测试代码
class CrashReproducer {
public:
    // 模拟可能导致崩溃的场景
    static void simulateCrash() {
        // 场景1：损坏的缓冲区
        char corrupted_buffer[256];
        memset(corrupted_buffer, 0xFF, sizeof(corrupted_buffer));  // 填充无效数据
        
        try {
            QString test = QString::fromUtf8(corrupted_buffer, 256);
            // test析构时可能崩溃
        } catch (...) {
            qDebug("Exception caught during QString creation");
        }
        
        // 场景2：多线程竞争
        std::thread t1([&]() {
            QString shared_string = "test";
            // 线程1修改字符串
        });
        
        std::thread t2([&]() {
            QString shared_string = "test";
            // 线程2同时修改字符串
        });
        
        t1.join();
        t2.join();
    }
    
    // 安全的替代实现
    static QString safeBufToString(const char* buf, int len) {
        // 参数验证
        if (!buf || len <= 0 || len > 1024*1024) {
            qWarning("Invalid buffer parameters: buf=%p, len=%d", buf, len);
            return QString();
        }
        
        try {
            // 创建安全的副本
            QByteArray safeBuffer(buf, len);
            
            // 验证数据有效性
            if (safeBuffer.isValidUtf8()) {
                return QString::fromUtf8(safeBuffer);
            } else {
                qWarning("Invalid UTF-8 data, using Latin-1");
                return QString::fromLatin1(safeBuffer);
            }
        } catch (const std::exception& e) {
            qCritical("Exception in string conversion: %s", e.what());
            return QString();
        }
    }
};

// 修复后的dev_msg_cb实现
void fixed_dev_msg_cb(void* net_handle, int msg_code, char* buf, int len, void* context) {
    // 严格的参数验证
    if (!buf || !context || len <= 0 || len > 65536) {
        qWarning("dev_msg_cb: Invalid parameters");
        return;
    }
    
    try {
        MainWindow* window = static_cast<MainWindow*>(context);
        
        // 第135行的安全实现：
        QString message = CrashReproducer::safeBufToString(buf, len);
        
        if (message.isEmpty()) {
            qWarning("Failed to convert buffer to string");
            return;
        }
        
        // 线程安全的处理
        if (QThread::currentThread() != window->thread()) {
            // 跨线程安全调用
            QMetaObject::invokeMethod(window, 
                [window, message, msg_code]() {
                    window->handleNetworkMessage(message, msg_code);
                },
                Qt::QueuedConnection);
        } else {
            // 同线程直接调用
            window->handleNetworkMessage(message, msg_code);
        }
        
    } catch (const std::exception& e) {
        qCritical("dev_msg_cb exception: %s", e.what());
    } catch (...) {
        qCritical("dev_msg_cb unknown exception");
    }
}

/*
=== 更新：新的崩溃信息分析 ===

新的GDB堆栈显示了不同的崩溃模式：

SIGSEGV崩溃堆栈：
#0  malloc() from /lib64/libc.so.6  <- 段错误发生在malloc内部
#6  QString::fromLocal8Bit() at qstring.h:671  <- 第135行调用
#7  MainWindow::dev_msg_cb() at mainwindow.cpp:135

关键差异：
1. 崩溃类型：SIGSEGV (段错误) vs 之前的SIGABRT (主动终止)
2. 崩溃阶段：QString构造 vs 之前的析构
3. 具体函数：fromLocal8Bit vs 之前的fromUtf8
4. 数据内容：可见JSON字符串数据

第135行的实际操作现在明确为：
QString::fromLocal8Bit(str, size)

其中str指向JSON数据：
{
    "chipsn": "3516d5002749096421232b08703905d74cd8030a0002900700000007",
    "hid": "",
    "topic": ""
}

新的崩溃原因分析：
1. 堆结构已被之前的操作损坏
2. malloc()尝试分配内存时访问损坏的堆元数据
3. 导致段错误而不是检测到的错误
4. 这可能是同一问题的级联效应

总结：第135行最可能的操作是：
QString message = QString::fromLocal8Bit(buf, len);  // 新发现

崩溃原因：
1. buf指向的内存已被其他线程修改或释放
2. len参数与buf实际大小不匹配
3. 网络数据包含无效的本地编码序列
4. 多线程环境下的QString引用计数错误
5. 内存分配失败或堆损坏 <- 现在在分配阶段就失败
6. 堆元数据损坏导致malloc()段错误

当函数调用QString::fromLocal8Bit()时，触发内存分配，
但由于堆损坏，malloc()访问无效内存地址导致SIGSEGV。
*/
