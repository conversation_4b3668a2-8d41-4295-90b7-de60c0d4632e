// malloc() SIGSEGV 深入分析
// 为什么malloc()函数内部会发生段错误

#include <cstdlib>
#include <cstring>
#include <iostream>

/*
新的崩溃堆栈分析：

#0  0x0000007f88c09c60 in malloc () from /lib64/libc.so.6
#1  0x0000007f890d1b4c in QArrayData::allocate() from Qt5Core
#2  0x0000007f89159fdc in QString::QString(int, Qt::Initialization) from Qt5Core  
#3  0x0000007f893050f0 in ?? () from Qt5Core
#4  0x0000007f89305830 in ?? () from Qt5Core
#5  0x0000007f8915fcd4 in QString::fromLocal8Bit_helper() from Qt5Core
#6  QString::fromLocal8Bit() at qstring.h:671
#7  MainWindow::dev_msg_cb() at mainwindow.cpp:135

关键信息：
- 段错误发生在malloc()内部，地址0x0000007f88c09c60
- 调用链：fromLocal8Bit -> QString构造 -> QArrayData分配 -> malloc
- 数据：JSON字符串，长度约100字节
*/

// 1. malloc()段错误的可能原因
class MallocSegfaultAnalysis {
public:
    // 分析malloc内部段错误的原因
    static void analyzeMallocSegfault() {
        /*
        malloc()内部发生SIGSEGV的可能原因：
        
        1. 堆元数据损坏
           - 堆的链表结构被破坏
           - 空闲块链表指针无效
           - 堆头部信息损坏
        
        2. 内存映射问题
           - 堆区域被意外unmap
           - 虚拟内存映射损坏
           - 内存保护属性异常
        
        3. 多线程竞争
           - 多个线程同时修改堆结构
           - 没有适当的锁保护
           - 竞争条件导致数据不一致
        
        4. 栈溢出影响
           - 栈溢出覆盖了堆管理数据
           - 函数调用栈损坏
        
        5. 内存分配器内部错误
           - glibc malloc实现的bug
           - 特定条件下的边界情况
        */
        
        explainHeapCorruption();
        explainMemoryMapping();
        explainThreadingIssues();
    }
    
private:
    static void explainHeapCorruption() {
        /*
        堆损坏导致malloc段错误的机制：
        
        malloc维护一个复杂的数据结构来管理堆内存：
        - 空闲块链表 (free list)
        - 堆段信息 (heap segments)  
        - 分配块头部信息 (chunk headers)
        
        当这些结构被损坏时，malloc尝试访问无效指针：
        */
        
        std::cout << "堆损坏分析：" << std::endl;
        
        // 模拟堆结构
        struct malloc_chunk {
            size_t prev_size;    // 前一个块的大小
            size_t size;         // 当前块大小和标志位
            malloc_chunk* fd;    // 前向指针 (空闲块)
            malloc_chunk* bk;    // 后向指针 (空闲块)
        };
        
        // 模拟损坏的堆链表
        malloc_chunk corrupted_chunk;
        corrupted_chunk.fd = (malloc_chunk*)0x12345678;  // 无效指针
        corrupted_chunk.bk = (malloc_chunk*)0x87654321;  // 无效指针
        
        std::cout << "损坏的空闲块指针：" << std::endl;
        std::cout << "fd = " << corrupted_chunk.fd << std::endl;
        std::cout << "bk = " << corrupted_chunk.bk << std::endl;
        
        // 当malloc尝试遍历空闲链表时：
        // malloc_chunk* next = current->fd;  <- 段错误
        std::cout << "malloc尝试访问fd指针时会发生段错误" << std::endl;
    }
    
    static void explainMemoryMapping() {
        /*
        内存映射问题：
        
        1. 堆区域被意外munmap()
        2. 虚拟内存地址空间损坏
        3. 内存保护属性被修改
        */
        
        std::cout << "\n内存映射问题分析：" << std::endl;
        std::cout << "可能的原因：" << std::endl;
        std::cout << "1. 堆区域被意外释放" << std::endl;
        std::cout << "2. 虚拟内存映射表损坏" << std::endl;
        std::cout << "3. 内存保护属性异常" << std::endl;
    }
    
    static void explainThreadingIssues() {
        /*
        多线程问题：
        
        glibc的malloc在多线程环境下使用arena机制，
        每个线程可能有自己的堆arena。
        如果arena结构被损坏，会导致段错误。
        */
        
        std::cout << "\n多线程问题分析：" << std::endl;
        std::cout << "1. 多个线程同时修改堆结构" << std::endl;
        std::cout << "2. Arena数据结构损坏" << std::endl;
        std::cout << "3. 线程本地存储异常" << std::endl;
    }
};

// 2. QString::fromLocal8Bit的内存分配过程
class QStringAllocationAnalysis {
public:
    // 分析QString内存分配的详细过程
    static void analyzeQStringAllocation() {
        /*
        QString::fromLocal8Bit的分配过程：
        
        1. fromLocal8Bit(str, size) 被调用
        2. 调用 fromLocal8Bit_helper(str, size)
        3. 创建新的QString对象 QString(int, Qt::Initialization)
        4. 调用 QArrayData::allocate() 分配内存
        5. 最终调用系统的 malloc()
        
        在第5步发生段错误，说明：
        - 前面的步骤都正常执行
        - 问题出现在系统级别的内存分配
        */
        
        simulateAllocationProcess();
    }
    
private:
    static void simulateAllocationProcess() {
        std::cout << "QString分配过程模拟：" << std::endl;
        
        // 模拟分配参数
        const char* json_str = R"({
    "chipsn": "3516d5002749096421232b08703905d74cd8030a0002900700000007",
    "hid": "",
    "topic": ""
})";
        
        size_t str_len = strlen(json_str);
        std::cout << "输入字符串长度：" << str_len << " 字节" << std::endl;
        
        // QString需要的内存计算
        size_t unicode_size = str_len * sizeof(ushort);  // UTF-16编码
        size_t header_size = sizeof(void*) * 4;          // QArrayData头部
        size_t total_size = header_size + unicode_size + sizeof(ushort); // +null终止符
        
        std::cout << "需要分配的内存：" << total_size << " 字节" << std::endl;
        std::cout << "包括：" << std::endl;
        std::cout << "  - QArrayData头部：" << header_size << " 字节" << std::endl;
        std::cout << "  - Unicode数据：" << unicode_size << " 字节" << std::endl;
        std::cout << "  - 空终止符：" << sizeof(ushort) << " 字节" << std::endl;
        
        // 这个大小的分配请求应该是正常的
        // 如果malloc在这里段错误，说明堆已经严重损坏
        std::cout << "\n结论：" << total_size << "字节的分配请求是合理的" << std::endl;
        std::cout << "malloc段错误表明堆结构已经损坏" << std::endl;
    }
};

// 3. JSON数据的影响分析
class JSONDataAnalysis {
public:
    // 分析JSON数据是否对崩溃有影响
    static void analyzeJSONData() {
        /*
        从堆栈中可以看到的JSON数据：
        {
            "chipsn": "3516d5002749096421232b08703905d74cd8030a0002900700000007",
            "hid": "",
            "topic": ""
        }
        
        分析：
        1. 数据格式正常，是有效的JSON
        2. 包含ASCII字符，对于Local8Bit编码应该没问题
        3. 长度约100字节，不算大
        4. 没有特殊的控制字符或无效编码
        
        结论：JSON数据本身不是问题的原因
        */
        
        const char* json_data = R"({
    "chipsn": "3516d5002749096421232b08703905d74cd8030a0002900700000007",
    "hid": "",
    "topic": ""
})";
        
        std::cout << "JSON数据分析：" << std::endl;
        std::cout << "长度：" << strlen(json_data) << " 字节" << std::endl;
        std::cout << "编码：纯ASCII字符" << std::endl;
        std::cout << "格式：有效JSON" << std::endl;
        std::cout << "结论：数据本身不是崩溃原因" << std::endl;
    }
};

// 4. 与之前SIGABRT的关联分析
class CrashCorrelationAnalysis {
public:
    // 分析两次崩溃的关联性
    static void analyzeCrashCorrelation() {
        /*
        SIGABRT vs SIGSEGV 关联分析：
        
        时间序列可能是：
        1. 第一次：网络回调创建QString，析构时检测到内存错误 -> SIGABRT
        2. 程序重启或继续运行
        3. 第二次：堆结构已经损坏，新的内存分配失败 -> SIGSEGV
        
        或者：
        1. 堆损坏是渐进的过程
        2. 最初表现为引用计数错误 (SIGABRT)
        3. 后来发展为堆结构完全损坏 (SIGSEGV)
        
        共同点：
        - 都在MainWindow::dev_msg_cb的第135行
        - 都涉及QString操作
        - 都是msg_code=2177的消息
        - 都在多线程环境下发生
        */
        
        std::cout << "崩溃关联性分析：" << std::endl;
        std::cout << "共同特征：" << std::endl;
        std::cout << "1. 相同的函数：MainWindow::dev_msg_cb" << std::endl;
        std::cout << "2. 相同的行号：135" << std::endl;
        std::cout << "3. 相同的消息类型：2177" << std::endl;
        std::cout << "4. 都涉及QString操作" << std::endl;
        std::cout << "5. 多线程环境" << std::endl;
        
        std::cout << "\n差异：" << std::endl;
        std::cout << "SIGABRT: 析构阶段，检测到错误主动终止" << std::endl;
        std::cout << "SIGSEGV: 构造阶段，内存访问违规" << std::endl;
        
        std::cout << "\n可能的演进过程：" << std::endl;
        std::cout << "1. 初期：引用计数错误 -> SIGABRT" << std::endl;
        std::cout << "2. 发展：堆结构损坏 -> SIGSEGV" << std::endl;
        std::cout << "3. 根本原因：多线程内存管理冲突" << std::endl;
    }
};

// 5. 堆损坏的检测方法
class HeapCorruptionDetection {
public:
    // 提供堆损坏的检测方法
    static void suggestDetectionMethods() {
        /*
        检测堆损坏的方法：
        
        1. 使用调试版本的malloc
        2. 启用堆检查工具
        3. 使用内存调试器
        4. 添加运行时检查
        */
        
        std::cout << "堆损坏检测方法：" << std::endl;
        std::cout << "1. 编译时：" << std::endl;
        std::cout << "   export MALLOC_CHECK_=2" << std::endl;
        std::cout << "   export MALLOC_PERTURB_=165" << std::endl;
        
        std::cout << "2. 运行时工具：" << std::endl;
        std::cout << "   valgrind --tool=memcheck" << std::endl;
        std::cout << "   AddressSanitizer (-fsanitize=address)" << std::endl;
        
        std::cout << "3. glibc调试：" << std::endl;
        std::cout << "   export LIBC_FATAL_STDERR_=1" << std::endl;
        std::cout << "   export MALLOC_TRACE=malloc.log" << std::endl;
        
        std::cout << "4. 自定义检查：" << std::endl;
        std::cout << "   定期调用malloc_stats()" << std::endl;
        std::cout << "   监控内存使用模式" << std::endl;
    }
};

/*
总结：malloc() SIGSEGV 分析

1. 根本原因：
   - 堆数据结构已经被损坏
   - malloc尝试访问无效的堆元数据
   - 导致段错误

2. 损坏来源：
   - 之前的多线程QString操作
   - 可能的缓冲区溢出
   - 内存管理错误的累积效应

3. 与SIGABRT的关系：
   - 可能是同一问题的不同阶段
   - SIGABRT是早期检测到的错误
   - SIGSEGV是后期堆完全损坏的表现

4. 关键特征：
   - 都发生在相同的代码位置
   - 都涉及QString的内存操作
   - 都在多线程环境下触发

5. 解决方向：
   - 修复多线程QString操作
   - 加强内存边界检查
   - 使用内存调试工具定位损坏源头
*/
