#include "vs_media_file.h"
#include <sys/stat.h>
#include <sys/statvfs.h>
#include <dirent.h>
#include <algorithm>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <sys/file.h>
#include <pthread.h>
#include <atomic>
#include <stdio.h>
#include <stdlib.h>
#include <set>

#include "record.h"
#include "channel.h"
#include "disk_manager.h"
#include "Thread/Lock.hpp"
#include "vs_comm_def.h"
#include "low_delay_common.h"

// ========== 第一阶段：添加缓存机制和USB独立管理 ==========

// 清理操作全局锁
static TCSLock g_cleanupLock;

// 全局变量，用于标记磁盘是否正在格式化
std::atomic<bool> g_disk_formatting(false);

// 静态UUID映射变量已移动到UUIDManager内部类中

// 单例相关静态成员初始化
DiskManager* DiskManager::m_instance = nullptr;
TCSLock DiskManager::m_instanceMutex;

// ========== 代码分离重构：MountInfo 结构体实现 ==========

// MountInfo 构造函数实现
MountInfo::MountInfo() : mountTime(0), isRemovable(false), isUsb(false),
                        totalSpace(0), availableSpace(0), lastChecked(0) {}

MountInfo::MountInfo(const std::string& dev, const std::string& mount, const std::string& fs)
    : device(dev), mountPoint(mount), fsType(fs), mountTime(time(NULL)),
      isRemovable(false), isUsb(false), totalSpace(0), availableSpace(0), lastChecked(0) {}

// ========== 代码分离重构：MountedDeviceCache 类实现 ==========

// MountedDeviceCache 构造函数和析构函数实现
MountedDeviceCache::MountedDeviceCache() : m_lastRefreshTime(0), m_refreshInProgress(false) {}

MountedDeviceCache::~MountedDeviceCache() {}

// 添加挂载设备到缓存
void MountedDeviceCache::addMountedDevice(const std::string& device, const MountInfo& info) {
    ScopedLocker lock(m_cacheMutex);
    m_mountedDevices[device] = info;
    LogI("缓存添加设备: %s -> %s", device.c_str(), info.mountPoint.c_str());
}
// 从缓存中移除设备
void MountedDeviceCache::removeMountedDevice(const std::string& device) {
    ScopedLocker lock(m_cacheMutex);
    auto it = m_mountedDevices.find(device);
    if (it != m_mountedDevices.end()) {
        LogI("缓存移除设备: %s", device.c_str());
        m_mountedDevices.erase(it);
    }
}

// 检查设备是否已挂载
bool MountedDeviceCache::isDeviceMounted(const std::string& device) const {
    ScopedLocker lock(m_cacheMutex);
    return m_mountedDevices.find(device) != m_mountedDevices.end();
}

// 获取设备挂载点
std::string MountedDeviceCache::getCacheDeviceMountPoint(const std::string& device) const {
	LogE("device = %s", device.c_str());
	
    ScopedLocker lock(m_cacheMutex);
    auto it = m_mountedDevices.find(device);
    if (it != m_mountedDevices.end()) {
		LogE("mount = %s", (it->second.mountPoint).c_str());
		return it->second.mountPoint ;
    }
	else {
		LogE("NULL!!");
		return "";
	}
}

// 获取所有已挂载设备
std::vector<MountInfo> MountedDeviceCache::getAllMountedDevices() const {
    ScopedLocker lock(m_cacheMutex);
    std::vector<MountInfo> result;
    for (const auto& pair : m_mountedDevices) {
        result.push_back(pair.second);
    }
    return result;
}
// 刷新缓存
void MountedDeviceCache::refreshCache() {
    if (m_refreshInProgress.exchange(true)) {
        return;  // 已有刷新在进行
    }

    try {
        // 获取系统挂载信息
        auto systemMountInfo = getSystemMountInfo();

        // 更新缓存
        {
            ScopedLocker lock(m_cacheMutex);
            m_mountedDevices = systemMountInfo;
        }

        m_lastRefreshTime.store(time(NULL));
        LogI("缓存刷新完成，设备数量: %zu", systemMountInfo.size());
    } catch (...) {
        LogE("缓存刷新失败");
    }

    m_refreshInProgress.store(false);
}

// 检查是否需要刷新
bool MountedDeviceCache::needsRefresh() const {
    time_t now = time(NULL);
    time_t lastRefresh = m_lastRefreshTime.load();
    return (now - lastRefresh) > 30;  // 30秒刷新间隔
}

// 从系统获取挂载信息
std::map<std::string, MountInfo> MountedDeviceCache::getSystemMountInfo() const {
    std::map<std::string, MountInfo> result;

    FILE* fp = fopen("/proc/mounts", "r");
    if (!fp) {
        LogE("无法打开 /proc/mounts");
        return result;
    }

    char line[1024];
    while (fgets(line, sizeof(line), fp)) {
        char device[256], mountPoint[256], fsType[64];
        if (sscanf(line, "%255s %255s %63s", device, mountPoint, fsType) == 3) {
            // 只处理块设备
            if (strncmp(device, "/dev/", 5) == 0) {
                MountInfo info(device, mountPoint, fsType);
                info.lastChecked = time(NULL);
                result[device] = info;
            }
        }
    }

    fclose(fp);
    return result;
}

// ========== 代码分离重构：UsbDeviceManager 类实现 ==========

// UsbDeviceManager 构造函数和析构函数实现
UsbDeviceManager::UsbDeviceManager(MountedDeviceCache* mountCache)
    : m_mountCache(mountCache), m_detectionRunning(false), m_checkIntervalSeconds(5) {}

UsbDeviceManager::~UsbDeviceManager() {
    stopDetection();
}

// 启动USB检测
bool UsbDeviceManager::startDetection() {
    ScopedLocker lock(m_usbManagerLock);

    if (m_detectionRunning.load()) {
        LogI("USB检测已在运行");
        return true;
    }

    if (pthread_create(&m_detectionThread, NULL, usbDetectionThreadFunc, this) != 0) {
        LogE("创建USB检测线程失败");
        return false;
    }

    m_detectionRunning.store(true);
    LogI("USB检测线程启动成功");
    return true;
}

// 停止USB检测
void UsbDeviceManager::stopDetection() {
    if (m_detectionRunning.load()) {
        m_detectionRunning.store(false);

        // 等待线程结束
        pthread_join(m_detectionThread, NULL);
        LogI("USB检测线程已停止");
    }
}

// 获取USB设备列表
std::vector<DiskInfo> UsbDeviceManager::getUsbDevices() const {
    ScopedLocker lock(m_usbManagerLock);
    return m_usbDevices;
}
// 挂载USB设备
bool UsbDeviceManager::mountUsbDevice(const std::string& device, const std::string& mountPoint) {
    ScopedLocker lock(m_usbManagerLock);

    // 检查设备是否已挂载
    if (m_mountCache && m_mountCache->isDeviceMounted(device)) {
        return true;
    }

    // 生成挂载点
    std::string actualMountPoint = mountPoint.empty() ?
        generateUsbMountPoint(device) : mountPoint;

    // 创建挂载点目录
    char mkdirCmd[512];
    snprintf(mkdirCmd, sizeof(mkdirCmd), "mkdir -p %s", actualMountPoint.c_str());
    int mkdirResult = system_no_fd(mkdirCmd);

    if (mkdirResult != 0) {
        LogE("创建挂载点目录失败: %s, 返回码: %d", actualMountPoint.c_str(), mkdirResult);
        return false;
    }

    // 检测文件系统类型
    std::string fsType = detectFilesystemType(device);

    // 构建挂载命令（添加常用的USB设备挂载选项）
    char mountCmd[512];
    if (fsType == "vfat" || fsType == "msdos") {
        // FAT文件系统需要特殊选项
        snprintf(mountCmd, sizeof(mountCmd), "mount -t %s -o rw,umask=0000,uid=0,gid=0 %s %s",
                 fsType.c_str(), device.c_str(), actualMountPoint.c_str());
    } else if (fsType == "ntfs") {
        // NTFS文件系统
        snprintf(mountCmd, sizeof(mountCmd), "mount -t ntfs -o rw,umask=0000,uid=0,gid=0 %s %s",
                 device.c_str(), actualMountPoint.c_str());
    } else {
        // 其他文件系统
        snprintf(mountCmd, sizeof(mountCmd), "mount -t %s %s %s",
                 fsType.c_str(), device.c_str(), actualMountPoint.c_str());
    }

    int result = system_no_fd(mountCmd);
    if (result == 0) {
        // 验证挂载是否真正成功
        if (access((actualMountPoint + "/.").c_str(), F_OK) == 0) {
            // 更新USB挂载点映射
            m_usbMountPoints[device] = actualMountPoint;

            // 更新挂载缓存
            if (m_mountCache) {
                MountInfo info(device, actualMountPoint, fsType);
                info.isUsb = true;
                info.isRemovable = true;
                m_mountCache->addMountedDevice(device, info);
            }

            return true;
        } else {
            LogE("USB设备挂载命令成功但验证失败: %s", device.c_str());

            // 清理失败的挂载点
            char cleanupCmd[512];
            snprintf(cleanupCmd, sizeof(cleanupCmd), "rmdir %s 2>/dev/null", actualMountPoint.c_str());
            system_no_fd(cleanupCmd);

            return false;
        }
    } 
	else {
        LogE("USB设备挂载失败: %s, 返回码: %d", device.c_str(), result);

        // 清理失败的挂载点
        char cleanupCmd[512];
        snprintf(cleanupCmd, sizeof(cleanupCmd), "rmdir %s 2>/dev/null", actualMountPoint.c_str());
        system_no_fd(cleanupCmd);

        return false;
    }
}
// 卸载USB设备
bool UsbDeviceManager::unmountUsbDevice(const std::string& device) {
    ScopedLocker lock(m_usbManagerLock);

    auto it = m_usbMountPoints.find(device);
    if (it == m_usbMountPoints.end()) {
        return false;
    }

    std::string mountPoint = it->second;

    // 执行卸载
    char umountCmd[512];
    snprintf(umountCmd, sizeof(umountCmd), "umount %s", mountPoint.c_str());

    int result = system_no_fd(umountCmd);
    if (result == 0) {
        // 移除挂载点映射
        m_usbMountPoints.erase(it);

        // 更新挂载缓存
        if (m_mountCache) {
            m_mountCache->removeMountedDevice(device);
        }

        LogI("USB设备卸载成功: %s", device.c_str());
        return true;
    } else {
        LogE("USB设备卸载失败: %s, 返回码: %d", device.c_str(), result);
        return false;
    }
}

// 更新USB设备状态
void UsbDeviceManager::updateUsbDeviceStatus() {
    ScopedLocker lock(m_usbManagerLock);

    // 扫描USB设备
    auto usbDevices = scanUsbDevices();

    // 更新设备列表
    m_usbDevices.clear();
    for (const auto& device : usbDevices) {
        DiskInfo info = getUsbDeviceInfo(device);
        m_usbDevices.push_back(info);
    }

	if (m_usbDevices.size() > 0){
    	LogI("USB设备状态更新完成，发现设备数: %zu", m_usbDevices.size());
	}
	
}

// 检查USB设备是否已挂载
bool UsbDeviceManager::isUsbDeviceMounted(const std::string& device) const {
    ScopedLocker lock(m_usbManagerLock);
    return m_usbMountPoints.find(device) != m_usbMountPoints.end();
}

// 获取USB设备挂载点
std::string UsbDeviceManager::getUsbDeviceMountPoint(const std::string& device) const {
    ScopedLocker lock(m_usbManagerLock);
    auto it = m_usbMountPoints.find(device);
    return (it != m_usbMountPoints.end()) ? it->second : "";
}

// USB检测线程函数
void* UsbDeviceManager::usbDetectionThreadFunc(void* arg) {
    UsbDeviceManager* manager = static_cast<UsbDeviceManager*>(arg);
    manager->usbDetectionWork();
    return NULL;
}

// USB检测工作逻辑
void UsbDeviceManager::usbDetectionWork() {
    LogI("USB检测线程开始工作");

    while (m_detectionRunning.load()) {
        try {
            updateUsbDeviceStatus();

            // 等待检测间隔
            for (int i = 0; i < m_checkIntervalSeconds && m_detectionRunning.load(); ++i) {
                sleep(1);
            }
        } catch (...) {
            LogE("USB检测工作异常");
        }
    }

    LogI("USB检测线程结束工作");
}

// 扫描USB设备
std::vector<std::string> UsbDeviceManager::scanUsbDevices() const {
    std::vector<std::string> devices;

    // 扫描 /dev/sd* 设备（改进：支持更多设备名格式）
    DIR* dir = opendir("/dev");
    if (dir) {
        struct dirent* entry;
        while ((entry = readdir(dir)) != NULL) {
            std::string entryName = entry->d_name;

            // 检查是否是sd设备的分区（sdb1, sdc1, sdd1等）
            if (entryName.length() >= 4 &&
                entryName.substr(0, 2) == "sd" &&
                entryName[2] >= 'b' && entryName[2] <= 'z' &&
                isdigit(entryName[3])) {

                std::string device = "/dev/" + entryName;

                // 检查设备是否存在且可访问
                if (access(device.c_str(), F_OK) == 0 && isUsbDevice(device)) {
                    devices.push_back(device);
                    LogI("发现USB设备: %s", device.c_str());
                }
            }
        }
        closedir(dir);
    }

    return devices;
}

// 获取USB设备信息
DiskInfo UsbDeviceManager::getUsbDeviceInfo(const std::string& device) const {
    DiskInfo info;
    info.device = device;
    info.path = device;
    info.isRemovable = true;
    info.lastChecked = time(NULL);

    // 检查是否已挂载
    if (m_mountCache && m_mountCache->isDeviceMounted(device)) {
        info.isMounted = true;
        info.path = m_mountCache->getCacheDeviceMountPoint(device);
        info.status = 0;
        info.statusMessage = "已挂载";
    } else {
        info.isMounted = false;
        info.status = 2;
        info.statusMessage = "未挂载";
    }

    return info;
}
// 生成USB挂载点
std::string UsbDeviceManager::generateUsbMountPoint(const std::string& device) const {
    // USB设备挂载路径规范：所有USB设备统一挂载到 /mnt/custom/usb
    // 严格分离固定磁盘路径(/mnt/custom/disk/)和可移动设备路径(/mnt/custom/usb)
    return "/mnt/custom/usb";
}

// 检查路径是否为USB设备路径
bool DiskManager::isUsbDevicePath(const std::string& path) const {
    // USB设备路径规范：/mnt/custom/usb 或其子目录
    return path.find("/mnt/custom/usb") == 0;
}

// 检测文件系统类型
std::string UsbDeviceManager::detectFilesystemType(const std::string& device) const {
    LogI("检测文件系统类型: %s", device.c_str());

    // 方法1：使用blkid检测文件系统类型
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "blkid -o value -s TYPE %s 2>/dev/null", device.c_str());

    FILE* fp = popen(cmd, "r");
    if (fp) {
        char fsType[64] = {0};
        if (fgets(fsType, sizeof(fsType), fp)) {
            // 移除换行符
            char* newline = strchr(fsType, '\n');
            if (newline) *newline = '\0';

            pclose(fp);

            if (strlen(fsType) > 0) {
                LogI("通过blkid检测到文件系统: %s -> %s", device.c_str(), fsType);
                return std::string(fsType);
            }
        }
        pclose(fp);
    }

    // 方法2：使用file命令作为备选
    snprintf(cmd, sizeof(cmd), "file -s %s 2>/dev/null", device.c_str());
    fp = popen(cmd, "r");
    if (fp) {
        char fileOutput[512] = {0};
        if (fgets(fileOutput, sizeof(fileOutput), fp)) {
            pclose(fp);

            std::string output = fileOutput;
            if (output.find("FAT") != std::string::npos) {
                LogI("通过file检测到FAT文件系统: %s", device.c_str());
                return "vfat";
            } else if (output.find("NTFS") != std::string::npos) {
                LogI("通过file检测到NTFS文件系统: %s", device.c_str());
                return "ntfs";
            } else if (output.find("ext") != std::string::npos) {
                LogI("通过file检测到ext文件系统: %s", device.c_str());
                return "ext4";
            }
        } else {
            pclose(fp);
        }
    }

    // 默认返回vfat（大多数USB设备使用FAT文件系统）
    LogW("无法检测文件系统类型，使用默认值: %s -> vfat", device.c_str());
    return "vfat";
}

// 检查是否是USB设备
bool UsbDeviceManager::isUsbDevice(const std::string& device) const {
    if (device.length() < 5 || device.substr(0, 5) != "/dev/") {
        return false;
    }

    // 从设备路径提取设备名（如从/dev/sdb1提取sdb）
    std::string deviceName = device.substr(5);
    std::string blockDevice = deviceName;

    // 移除分区号，获取块设备名（如从sdb1获取sdb）
    for (size_t i = 0; i < blockDevice.length(); i++) {
        if (isdigit(blockDevice[i])) {
            blockDevice = blockDevice.substr(0, i);
            break;
        }
    }

    // 检查设备的sys路径
    std::string sysPath = "/sys/block/" + blockDevice + "/removable";

    FILE* fp = fopen(sysPath.c_str(), "r");
    if (fp) {
        char removable[8] = {0};
        if (fgets(removable, sizeof(removable), fp)) {
            fclose(fp);
            bool isRemovable = (removable[0] == '1');

            if (isRemovable) {
                LogI("检测到可移动设备: %s", device.c_str());
            }

            return isRemovable;
        }
        fclose(fp);
    }

    // 如果无法通过sys路径检测，检查设备名模式
    // USB设备通常是sdb, sdc, sdd等（排除sda，通常是系统盘）
    if (blockDevice.length() == 3 &&
        blockDevice.substr(0, 2) == "sd" &&
        blockDevice[2] >= 'b' && blockDevice[2] <= 'z') {
        LogI("通过设备名模式检测到可能的USB设备: %s", device.c_str());
        return true;
    }

    return false;
}

// 前向声明阻塞式挂载相关函数
static bool finalizeDiskManagerInitialization(DiskManager* diskManager,
                                             const std::vector<std::string>& mountedDiskPaths);




// ============================================================================
// UUIDManager 外部类实现
// ============================================================================

// UUIDManager 构造函数
UUIDManager::UUIDManager() : m_lastLoadTime(0)
{
    LogI("UUIDManager 初始化");
}

// UUIDManager 析构函数
UUIDManager::~UUIDManager()
{
    LogI("UUIDManager 清理");
}

// Get disk UUID using blkid command
std::string UUIDManager::getDiskUUID(const std::string& device) const
{
    char cmd[256];
    char buffer[128];
    std::string uuid;

    // Use blkid to get UUID
    snprintf(cmd, sizeof(cmd), "blkid -s UUID -o value %s", device.c_str());
    FILE* fp = popen(cmd, "r");
    if (!fp) {
        LogE("获取磁盘UUID失败: 无法执行blkid命令");
        return "";
    }

    if (fgets(buffer, sizeof(buffer), fp) != NULL) {
        // Remove newline character
        size_t len = strlen(buffer);
        if (len > 0 && buffer[len - 1] == '\n') {
            buffer[len - 1] = '\0';
        }
        uuid = buffer;
    }
    pclose(fp);

    if (uuid.empty()) {
        LogW("设备 %s 没有UUID", device.c_str());
    }

    return uuid;
}

// Load UUID mappings from file
bool UUIDManager::loadUUIDMappings()
{
    ScopedLocker lock(m_uuidMutex);

    time_t now = time(NULL);

    // 如果5秒内已经加载过，直接返回成功
    if (now - m_lastLoadTime < 5 && !m_uuidMappings.empty()) {
        return true;
    }

    FILE* fp = fopen(UUID_MAPPING_FILE, "r");
    if (!fp) {
        LogW("UUID映射文件不存在，将创建新文件: %s", UUID_MAPPING_FILE);
        // 创建空映射文件
        saveUUIDMappings();
        m_lastLoadTime = now;
        return true;
    }

    m_uuidMappings.clear();
    char line[256];

    while (fgets(line, sizeof(line), fp) != NULL) {
        // Skip empty lines and comments
        if (line[0] == '\n' || line[0] == '#' || line[0] == '\0')
            continue;

        // Remove newline character
        size_t len = strlen(line);
        if (len > 0 && line[len - 1] == '\n') {
            line[len - 1] = '\0';
        }

        // Parse line format: UUID|MountPoint|Label|LastMountedTimestamp
        std::string uuid, mountPoint, label;
        time_t lastMounted = 0;

        char* token = strtok(line, "|");
        if (token) uuid = token;

        token = strtok(NULL, "|");
        if (token) mountPoint = token;

        token = strtok(NULL, "|");
        if (token) label = token;

        token = strtok(NULL, "|");
        if (token) lastMounted = atol(token);

        if (!uuid.empty() && !mountPoint.empty()) {
            UuidMapping mapping;
            mapping.uuid = uuid;
            mapping.mountPoint = mountPoint;
            mapping.label = label;
            mapping.lastMounted = lastMounted;
            m_uuidMappings.push_back(mapping);
        }
    }

    fclose(fp);
    LogI("加载了 %zu 条UUID映射", m_uuidMappings.size());
    m_lastLoadTime = now;
    return true;
}

// Save UUID mappings to file
bool UUIDManager::saveUUIDMappings()
{
    ScopedLocker lock(m_uuidMutex);

    FILE* fp = fopen(UUID_MAPPING_FILE, "w");
    if (!fp) {
        LogE("无法创建UUID映射文件: %s", UUID_MAPPING_FILE);
        return false;
    }

    // Write header
    fprintf(fp, "# UUID映射文件 - 请勿手动修改\n");
    fprintf(fp, "# 格式: UUID|挂载点|标签|最后挂载时间戳\n");

    for (const auto& mapping : m_uuidMappings) {
        fprintf(fp, "%s|%s|%s|%ld\n",
            mapping.uuid.c_str(),
            mapping.mountPoint.c_str(),
            mapping.label.c_str(),
            mapping.lastMounted);
    }

    fclose(fp);
    // Set appropriate permissions
    chmod(UUID_MAPPING_FILE, 0644);
    return true;
}

// Get mount point by UUID
std::string UUIDManager::getMountPointByUUID(const std::string& uuid) const
{
    if (uuid.empty()) {
        return "";
    }

    // 确保映射已加载
    const_cast<UUIDManager*>(this)->loadUUIDMappings();

    ScopedLocker lock(m_uuidMutex);

    for (const auto& mapping : m_uuidMappings) {
        if (mapping.uuid == uuid) {
            return mapping.mountPoint;
        }
    }

    return "";
}

// Get next available mount point
std::string UUIDManager::getNextAvailableMountPoint() const
{
    // 使用简化的挂载点分配策略，避免直接调用DiskManager的private方法
    // 基于DISK_MAPPING宏的标准挂载点格式
    std::string basePath = "/mnt/custom/disk/";

    // 尝试disk1到disk8的挂载点
    for (int i = 1; i <= 8; i++) {
        std::string mountPoint = basePath + "disk" + std::to_string(i);

        // 检查挂载点是否已被使用
        bool inUse = false;
        for (const auto& uuidMapping : m_uuidMappings) {
            if (uuidMapping.mountPoint == mountPoint) {
                inUse = true;
                break;
            }
        }

        if (!inUse) {
            return mountPoint;
        }
    }

    // 如果所有挂载点都被使用，返回第一个作为备用
    std::string fallback = basePath + "disk1";
    return fallback;
}

// Add UUID mapping
void UUIDManager::addUUIDMapping(const std::string& uuid, const std::string& mountPoint)
{
    if (uuid.empty() || mountPoint.empty()) {
        LogE("无法添加UUID映射: UUID或挂载点为空");
        return;
    }

    ScopedLocker lock(m_uuidMutex);

    // Check if UUID already exists
    for (auto& mapping : m_uuidMappings) {
        if (mapping.uuid == uuid) {
            if (mapping.mountPoint != mountPoint) {
                mapping.mountPoint = mountPoint;
                mapping.lastMounted = time(NULL);
                saveUUIDMappings();
            } else {
                // Just update timestamp
                mapping.lastMounted = time(NULL);
            }
            return;
        }
    }

    // Add new mapping
    UuidMapping mapping;
    mapping.uuid = uuid;
    mapping.mountPoint = mountPoint;
    mapping.label = "";
    mapping.lastMounted = time(NULL);
    m_uuidMappings.push_back(mapping);

    LogI("新增UUID映射: %s --> %s", uuid.c_str(), mountPoint.c_str());
    saveUUIDMappings();
}

// Remove UUID mapping by UUID
bool UUIDManager::removeUUIDMapping(const std::string& uuid)
{
    if (uuid.empty()) {
        LogE("无法删除UUID映射: UUID为空");
        return false;
    }

    ScopedLocker lock(m_uuidMutex);

    auto it = std::find_if(m_uuidMappings.begin(), m_uuidMappings.end(),
                          [&uuid](const UuidMapping& mapping) {
                              return mapping.uuid == uuid;
                          });

    if (it != m_uuidMappings.end()) {
        LogI("删除UUID映射: %s --> %s", uuid.c_str(), it->mountPoint.c_str());
        m_uuidMappings.erase(it);
        saveUUIDMappings();
        return true;
    } else {
        LogW("未找到要删除的UUID映射: %s", uuid.c_str());
        return false;
    }
}

// Remove UUID mapping by device path
bool UUIDManager::removeUUIDMappingByDevice(const std::string& devicePath)
{
    if (devicePath.empty()) {
        LogE("无法删除UUID映射: 设备路径为空");
        return false;
    }

    // 获取设备的UUID
    std::string uuid = getDiskUUID(devicePath);
    if (uuid.empty()) {
        LogW("无法获取设备UUID，无法删除映射: %s", devicePath.c_str());
        return false;
    }

    LogI("根据设备路径删除UUID映射: %s (UUID: %s)", devicePath.c_str(), uuid.c_str());
    return removeUUIDMapping(uuid);
}

// Validate UUID consistency for a device
bool UUIDManager::validateUUIDConsistency(const std::string& devicePath) const
{
    if (devicePath.empty()) {
        LogE("无法验证UUID一致性: 设备路径为空");
        return false;
    }

    // 获取设备当前的UUID
    std::string currentUuid = getDiskUUID(devicePath);
    if (currentUuid.empty()) {
        LogW("设备无UUID，跳过一致性验证: %s", devicePath.c_str());
        return true; // 无UUID不算错误
    }

    ScopedLocker lock(m_uuidMutex);

    // 检查是否存在重复的UUID映射
    int uuidCount = 0;
    std::string mappedMountPoint;

    for (const auto& mapping : m_uuidMappings) {
        if (mapping.uuid == currentUuid) {
            uuidCount++;
            mappedMountPoint = mapping.mountPoint;
        }
    }

    if (uuidCount == 0) {
        LogI("UUID未在映射表中，一致性正常: %s (UUID: %s)", devicePath.c_str(), currentUuid.c_str());
        return true;
    } else if (uuidCount == 1) {
        LogI("UUID映射一致性正常: %s --> %s", currentUuid.c_str(), mappedMountPoint.c_str());
        return true;
    } else {
        LogE("发现重复UUID映射，一致性异常: %s (重复次数: %d)", currentUuid.c_str(), uuidCount);
        return false;
    }
}

// Clean up duplicate UUIDs (keep only the most recent one)
void UUIDManager::cleanupDuplicateUUIDs(const std::string& uuid)
{
    if (uuid.empty()) {
        LogE("无法清理重复UUID: UUID为空");
        return;
    }

    ScopedLocker lock(m_uuidMutex);

    std::vector<UuidMapping> duplicates;

    // 找到所有重复的UUID映射
    for (const auto& mapping : m_uuidMappings) {
        if (mapping.uuid == uuid) {
            duplicates.push_back(mapping);
        }
    }

    if (duplicates.size() <= 1) {
        LogI("UUID无重复，无需清理: %s", uuid.c_str());
        return;
    }

    LogW("发现重复UUID映射，开始清理: %s (重复数量: %zu)", uuid.c_str(), duplicates.size());

    // 找到最新的映射（lastMounted时间最大的）
    auto latest = std::max_element(duplicates.begin(), duplicates.end(),
                                  [](const UuidMapping& a, const UuidMapping& b) {
                                      return a.lastMounted < b.lastMounted;
                                  });

    // 删除所有UUID映射
    m_uuidMappings.erase(
        std::remove_if(m_uuidMappings.begin(), m_uuidMappings.end(),
                      [&uuid](const UuidMapping& mapping) {
                          return mapping.uuid == uuid;
                      }),
        m_uuidMappings.end());

    // 重新添加最新的映射
    m_uuidMappings.push_back(*latest);

    LogI("清理完成，保留最新UUID映射: %s --> %s (时间: %ld)",
         uuid.c_str(), latest->mountPoint.c_str(), latest->lastMounted);

    // 保存到文件
    saveUUIDMappings();
}

// DiskManager 构造函数
DiskManager::DiskManager() :
    m_diskPaths(),
    m_diskInfoMap(),
    m_activeDiskIndex(0),
    m_activeDiskPath(),
    m_running(false),
    m_isCleaningUp(false),
    m_cleanupRequested(false),  // 按照头文件中的声明顺序
    m_cleanupChannelId(-1),
    m_cleanupBytesToFree(0),
    m_cleanupThread(),
    m_cleanupMutex(),
    m_cleanupCond(),
    m_periodicCheckThread(),
    m_checkIntervalMinutes(DISK_CLEANUP_INTERVAL_MINUTES),
    m_threadStates(),  // 统一的线程状态管理
    m_lastCleanupTime(0),
    m_moduleInitialized(false),  // 按照头文件中的声明顺序
    // 第一阶段新增成员变量
    m_mountCache(nullptr),
    m_usbManager(nullptr),
    m_uuidManager(nullptr),
    // 平滑过渡机制状态变量
    m_diskSwitchPending(false),
    m_pendingSwitchTarget(),
    m_pendingSwitchLock(),
    m_pendingSwitchSetTime(0),
    // 优化：硬盘状态缓存机制
    m_diskStatusCache(),
    m_diskStatusCacheLock()
{
    LogI("DiskManager构造函数");

    // 初始化挂载设备缓存
    m_mountCache = new MountedDeviceCache();
    if (m_mountCache) {
    } else {
        LogE("挂载设备缓存初始化失败");
    }

    // 初始化USB设备管理器
    m_usbManager = new UsbDeviceManager(m_mountCache);
    if (m_usbManager) {
        LogI("USB设备管理器初始化成功");
    } else {
        LogE("USB设备管理器初始化失败");
    }

    // 初始化UUID管理器
    m_uuidManager = new UUIDManager();
    if (m_uuidManager) {
        LogI("UUID管理器初始化成功");
        // 加载UUID映射
        m_uuidManager->loadUUIDMappings();
    } else {
        LogE("UUID管理器初始化失败");
    }

    // 获取DISK_MAPPING中定义的所有有效挂载点
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
    std::set<std::string> validMountPoints;
    
    // 收集所有来自DISK_MAPPING的挂载点
    for (int i = 0; i < MAX_DISK_COUNT; i++) {
        if (disks[i].mountpoint && *disks[i].mountpoint) {
            std::string mountPoint = disks[i].mountpoint;
            
            // 对于挂载检查，不需要末尾斜杠
            // 保留原始路径格式
            
            validMountPoints.insert(mountPoint);
        }
    }
    
    // 初始化磁盘路径列表（只添加已挂载的DISK_MAPPING中的磁盘）
    int mountedCount = 0;

    std::string firstMountedDisk; // 记录第一个已挂载的磁盘
    
    for (const auto& mountPoint : validMountPoints) {
        bool mounted = isMounted(mountPoint);
        bool critical = false;

        
        if (mounted) {
            // 记录第一个已挂载的磁盘
            if (firstMountedDisk.empty()) {
                firstMountedDisk = mountPoint;
            }
            
            // 检查空间状态
            critical = isCriticalSpace(mountPoint);
            // 获取详细信息
            struct statvfs stat;
            if (statvfs(mountPoint.c_str(), &stat) == 0) {
                double avail = stat.f_bavail;
                double total = stat.f_blocks;
                double percent = (avail / total) * 100.0;
            }
            
            // USB路径分离：排除USB设备路径，只添加固定磁盘路径
            if (!isUsbDevicePath(mountPoint)) {
                m_diskPaths.push_back(mountPoint);
            }
            mountedCount++;
        }
    }

    
    // 设置活动磁盘路径为第一个已挂载的磁盘
    if (!firstMountedDisk.empty()) {
        m_activeDiskPath = firstMountedDisk;
    }
    // 如果没有已挂载的磁盘，只能使用默认值
    else if (!validMountPoints.empty()) {
        m_activeDiskPath = *validMountPoints.begin();
        // USB路径分离：确保默认路径不是USB设备路径
        if (!isUsbDevicePath(m_activeDiskPath)) {
            m_diskPaths.push_back(m_activeDiskPath);
        }
    }
}

// DiskManager 析构函数（第一阶段优化）
DiskManager::~DiskManager()
{
    // 停止各线程，使用标志位通知线程退出，不等待线程结束

    // 第一阶段：停止USB管理器
    if (m_usbManager) {
        m_usbManager->stopDetection();
        delete m_usbManager;
        m_usbManager = nullptr;
        LogI("USB设备管理器已清理");
    }

    // 清理UUID管理器
    if (m_uuidManager) {
        delete m_uuidManager;
        m_uuidManager = nullptr;
        LogI("UUID管理器已清理");
    }

    // 停止清盘线程
    if (m_threadStates.cleanup.load()) {
        m_threadStates.cleanup.store(false);
        {
            ScopedLocker lock(m_cleanupMutex);
            m_cleanupCond.NotifyOne();
        }

        // 线程已分离，不需要等待线程结束
        LogI("已通知清盘线程退出");
    }

    // 停止定期磁盘检查线程
    if (m_threadStates.periodicCheck.load()) {
        m_threadStates.periodicCheck.store(false);
        LogI("已通知定期磁盘检查线程退出");
    }

    // USB检测线程已删除

    // 通知后台任务线程停止（后台任务锁已删除，直接通知）
    m_backgroundTaskCond.NotifyAll();

    // 第一阶段：清理挂载缓存
    if (m_mountCache) {
        delete m_mountCache;
        m_mountCache = nullptr;
        LogI("挂载设备缓存已清理");
    }

    LogI("DiskManager 析构完成 - 第一阶段优化");
}


// DiskManager 启动时初始化（只能在主程序启动时调用一次）
bool DiskManager::initializeOnStartup()
{
    // 使用ScopedLocker代替直接的互斥锁
    ScopedLocker lock(g_cleanupLock);

    // 检查是否已经初始化过
    if (m_moduleInitialized) {
        LogW("DiskManager模块已经初始化过，禁止重复初始化");
        return true;
    }

    // 执行实际的初始化逻辑
    bool result = initializeInternal();

    if (result) {
        m_moduleInitialized = true;
        LogI("DiskManager 启动时初始化成功");
    } else {
        LogE("DiskManager 启动时初始化失败");
    }

    return result;
}

// 从DISK_MAPPING更新磁盘列表
void DiskManager::updateDiskListFromMapping(const std::string& mountPoint)
{
    LogI("从DISK_MAPPING更新磁盘列表: %s", mountPoint.c_str());

    // 检查是否已经在列表中
    for (const auto& path : m_diskPaths) {
        if (path == mountPoint) {
            LogI("磁盘路径已存在于列表中: %s", mountPoint.c_str());
            return;
        }
    }

    // USB路径分离：只有非USB路径才添加到磁盘管理列表
    if (!isUsbDevicePath(mountPoint)) {
        m_diskPaths.push_back(mountPoint);
    } else {
        LogW("跳过USB设备路径，不添加到磁盘管理列表: %s", mountPoint.c_str());
    }

    // 设置为活动磁盘
    m_activeDiskPath = mountPoint;
    m_activeDiskIndex = 0;

    LogI("已将磁盘添加到路径列表并设置为活动磁盘: %s", mountPoint.c_str());
}

// 磁盘路径管理辅助方法实现
void DiskManager::clearDiskPaths()
{
    m_diskPaths.clear();
    LogI("清空磁盘路径列表");
}

void DiskManager::addDiskPath(const std::string& path)
{
    // USB路径分离：只添加非USB设备路径
    if (!isUsbDevicePath(path)) {
        m_diskPaths.push_back(path);
    } else {
        LogW("拒绝添加USB设备路径到磁盘管理列表: %s", path.c_str());
    }
}

void DiskManager::setActiveDiskPathInternal(const std::string& path)
{
    m_activeDiskPath = path;
    m_activeDiskIndex = 0;

    // 更新活动磁盘索引
    for (size_t i = 0; i < m_diskPaths.size(); i++) {
        if (m_diskPaths[i] == path) {
            m_activeDiskIndex = i;
            break;
        }
    }
}

void DiskManager::setModuleInitialized(bool initialized)
{
    m_moduleInitialized = initialized;
}

// DiskManager 内部初始化实现（原 init 方法的实现）
bool DiskManager::initializeInternal()
{
    // 注意：调用者已经获取了锁，这里不需要再次获取

    LogI("执行磁盘内部初始化");

    // 加载UUID映射
    m_uuidManager->loadUUIDMappings();

    LogI("======== 开始阻塞挂载所有固定磁盘 ========");

    // 获取DISK_MAPPING中定义的所有有效挂载点
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
    std::set<std::string> validMountPoints;

    // 挂载统计
    int totalDisks = 0;
    int existingDisks = 0;
    int alreadyMounted = 0;
    int successfulMounts = 0;
    int failedMounts = 0;

    // 第一阶段：遍历所有DISK_MAPPING中的固定磁盘，执行阻塞挂载
    for (int i = 0; i < MAX_DISK_COUNT; i++) {
        if (disks[i].mountpoint && *disks[i].mountpoint &&
            disks[i].device && *disks[i].device) {

            totalDisks++;
            std::string mountPoint = disks[i].mountpoint;
            std::string devicePath = disks[i].device;

            // 检查是否是移动设备，跳过移动磁盘
            if (isRemovableDevice(devicePath)) {
                LogI("跳过移动设备 [%d/%d]: %s -> %s", i+1, MAX_DISK_COUNT, devicePath.c_str(), mountPoint.c_str());
                continue;
            }

            LogI("检查固定磁盘 [%d/%d]: %s -> %s", i+1, MAX_DISK_COUNT, devicePath.c_str(), mountPoint.c_str());

            // 检查设备是否存在
            if (!isDeviceExists(devicePath)) {
                LogI("设备不存在或不是块设备，跳过: %s", devicePath.c_str());
                continue;
            }

            existingDisks++;

            // 检查是否已挂载
            bool mounted = isMounted(mountPoint);
            if (mounted) {
                LogI("磁盘已挂载: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                alreadyMounted++;

                // 对已挂载的磁盘使用统一清盘触发检查
                checkAndTriggerCleanupIfNeeded(mountPoint, "initializeInternal-existing");
            } 
			else {
                LogI("执行阻塞挂载: %s -> %s", devicePath.c_str(), mountPoint.c_str());

                // 使用统一的挂载接口
                bool mountSuccess = mountDiskInternal(devicePath);
                if (mountSuccess) {
                    LogI("阻塞挂载成功: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                    successfulMounts++;

                    // 对新挂载的磁盘使用统一清盘触发检查
                    checkAndTriggerCleanupIfNeeded(mountPoint, "initializeInternal-new");

                    // 挂载成功后延时，避免连续挂载时的冲突
                    if (successfulMounts > 0) {
                        LogI("挂载延时500ms，避免多磁盘挂载冲突...");
                        Sleep(500);
                    }
                } 
				else {
                    LogW("阻塞挂载失败: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                    failedMounts++;
                }
            }
        }
    }

    // 输出挂载统计信息
    LogI("======== 阻塞挂载阶段完成 ========");
    LogI("挂载统计: 总磁盘=%d, 存在设备=%d, 已挂载=%d, 新挂载成功=%d, 挂载失败=%d",
         totalDisks, existingDisks, alreadyMounted, successfulMounts, failedMounts);

    if (failedMounts > 0) {
        LogW("有 %d 个磁盘挂载失败，请检查设备状态", failedMounts);
    }

    if (existingDisks == 0) {
        LogW("没有检测到任何存储设备，系统将以静默模式运行");
    } else if (alreadyMounted + successfulMounts == 0) {
        LogW("没有任何磁盘挂载成功，录像功能将不可用");
    } else {
        LogI("成功挂载 %d 个磁盘，录像功能可用", alreadyMounted + successfulMounts);
    }

    // 阻塞挂载阶段完成后，直接构建磁盘路径列表
    LogI("======== 构建磁盘路径列表（基于挂载结果） ========");

    // 清空磁盘路径列表
    m_diskPaths.clear();

    // 基于阻塞挂载的结果，按DISK_MAPPING顺序构建磁盘路径列表（使用统一接口）
    auto diskMappings = getAllDiskMappings();
    int mountedCount = 0;

    for (const auto& mapping : diskMappings) {
        const std::string& mountPoint = mapping.second;

        // 只检查挂载状态，不重复挂载操作（阻塞挂载阶段已完成）
        if (isMounted(mountPoint)) {
            // USB路径分离：只添加非USB设备路径到磁盘管理列表
            if (!isUsbDevicePath(mountPoint)) {
                m_diskPaths.push_back(mountPoint);

                // 使用统一接口检查磁盘状态
                double usagePercent = getDiskUsagePercent(mountPoint, true);
                LogI("添加已挂载磁盘到路径列表: %s (已用: %.2f%%)", mountPoint.c_str(), usagePercent);
            } else {
                LogI("跳过USB设备路径: %s", mountPoint.c_str());
            }

            // 使用统一清盘触发检查
            checkAndTriggerCleanupIfNeeded(mountPoint, "initializeInternal-list");

            mountedCount++;
        }
    }

    LogI("磁盘路径列表构建完成，共添加了%d个已挂载的磁盘", mountedCount);

    // 选择最小编号的已挂载磁盘作为活动磁盘
    if (!m_diskPaths.empty()) {
        m_activeDiskIndex = 0;
        m_activeDiskPath = m_diskPaths[0];

        // 使用统一接口检查活动磁盘状态
        double activeDiskUsage = getDiskUsagePercent(m_activeDiskPath, true);
        LogI("选择活动磁盘: %s (最小编号, 已用: %.2f%%)", m_activeDiskPath.c_str(), activeDiskUsage);
    } else {
        m_activeDiskIndex = 0;
        m_activeDiskPath = "";
        LogW("没有可用的已挂载磁盘，将启用静默模式");
        setSilentMode(true);
    }

    m_running = false;
    
    // 磁盘切换尝试次数变量已删除

    LogI("磁盘管理器内部初始化完成: 路径数量=%zu", m_diskPaths.size());

    // 启动清盘线程（如果尚未启动）
    {
        ScopedLocker cleanupLock(m_cleanupMutex);

        if (!m_threadStates.cleanup.load()) {
            m_threadStates.cleanup.store(true);
            int ret = pthread_create(&m_cleanupThread, NULL, cleanupThreadFunc, this);
            if (ret != 0) {
                LogE("创建清盘线程失败: ret = %d", ret);
                m_threadStates.cleanup.store(false);
                return false;
            } else {
                // 分离线程，使其在结束时自动释放资源
                pthread_detach(m_cleanupThread);
                LogI("清盘线程创建成功，并已分离");
            }
        } else {
            LogI("清盘线程已经在运行中");
        }
    }

    // 启动定期磁盘检查线程
    if (!m_threadStates.periodicCheck.load()) {
        m_threadStates.periodicCheck.store(true);
        int ret = pthread_create(&m_periodicCheckThread, NULL, periodicCheckThreadFunc, this);
        if (ret != 0) {
            LogE("创建定期磁盘检查线程失败: ret = %d", ret);
            m_threadStates.periodicCheck.store(false);
            return false;
        } else {
            // 分离线程，使其在结束时自动释放资源
            pthread_detach(m_periodicCheckThread);
            LogI("定期磁盘检查线程创建成功，检查间隔=%d分钟，并已分离", m_checkIntervalMinutes);
        }
    } else {
        LogI("定期磁盘检查线程已经在运行中");
    }

    return true;
}

// 获取单例实例
DiskManager* DiskManager::getInstance()
{
    // 双重检查锁定模式
    if (m_instance == nullptr) {
        m_instanceMutex.Enter();
        if (m_instance == nullptr) {
            m_instance = new DiskManager();
            LogI("DiskManager 单例实例创建成功");
        }
        m_instanceMutex.Leave();
    }
    return m_instance;
}

// 销毁单例实例
void DiskManager::destroyInstance()
{
    m_instanceMutex.Enter();
    if (m_instance != nullptr) {
        LogI("销毁 DiskManager 单例实例");
        delete m_instance;
        m_instance = nullptr;
    }
    m_instanceMutex.Leave();
}

void DiskManager::startChecking()
{
    ScopedLocker lock(g_cleanupLock);
    m_running = true;
    
    LogW("开始磁盘监控");
}

void DiskManager::stopChecking()
{
    ScopedLocker lock(g_cleanupLock);
    m_running = false;
    
    LogW("停止磁盘监控");
}

// getTopDir() 方法已删除，功能可通过 getActiveDiskPath() 替代

// 获取活动磁盘路径
std::string DiskManager::getActiveDiskPath() const
{
    // 静态变量记录上次错误日志时间，避免频繁打印
    static time_t last_error_log_time = 0;
    static int consecutive_no_disk_count = 0;

    // 首先检查是否有任何已挂载的磁盘
    if (!hasAvailableDisk()) {
        time_t current_time = time(nullptr);
        consecutive_no_disk_count++;

        // 如果连续50次检查都没有磁盘，自动启用静默模式
        if (!m_silentMode.load() && consecutive_no_disk_count >= 50) {
            LogW("连续 %d 次检查都没有可用磁盘，自动启用静默模式以减少日志输出", consecutive_no_disk_count);
            LogW("如需重新启用磁盘日志，请调用 setSilentMode(false)");
            m_silentMode.store(true);
        }

        // 只在非静默模式下且满足时间条件时打印日志
        if (!m_silentMode.load() && (last_error_log_time == 0 || (current_time - last_error_log_time) >= 300)) {
            if (consecutive_no_disk_count == 1) {
                LogW("提示：没有检测到已挂载的磁盘，如果不需要录像功能可忽略此信息");
            } else {
                LogW("磁盘状态：仍然没有可用的已挂载磁盘 (已持续 %d 次检查)", consecutive_no_disk_count);
            }
            last_error_log_time = current_time;
        }

        // 没有挂载磁盘时返回空字符串，不返回默认路径
        return "";
    }

    // 有可用磁盘时重置计数器
    consecutive_no_disk_count = 0;
    
    std::string result;
    
    // 如果设置了活动磁盘路径，首先检查它是否已挂载
    if (!m_activeDiskPath.empty() && isMounted(m_activeDiskPath)) {
        result = m_activeDiskPath;
    }
    // 如果活动磁盘未挂载，从磁盘列表中找一个已挂载的
    else if (!m_diskPaths.empty()) {
        // 首先检查当前索引指向的磁盘
        if (m_activeDiskIndex < m_diskPaths.size() && 
            isMounted(m_diskPaths[m_activeDiskIndex])) {
            result = m_diskPaths[m_activeDiskIndex];
        }
        else {
            // 遍历所有磁盘寻找第一个已挂载的
            for (size_t i = 0; i < m_diskPaths.size(); i++) {
                if (isMounted(m_diskPaths[i])) {
                    result = m_diskPaths[i];
                    // 更新活动索引
                    const_cast<DiskManager*>(this)->m_activeDiskIndex = i;
                    break;
                }
            }
        }
    }
    
    // 如果仍然没有找到已挂载的磁盘，检查DISK_MAPPING中定义的磁盘
    if (result.empty()) {
        MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
        for (int i = 0; i < MAX_DISK_COUNT; i++) {
            if (disks[i].mountpoint && *disks[i].mountpoint) {
                std::string mountPoint = disks[i].mountpoint;
                if (isMounted(mountPoint)) {
                    result = mountPoint;
                    LogI("从DISK_MAPPING中找到已挂载的磁盘: %s", mountPoint.c_str());

                    // 调用非const方法来更新磁盘列表
                    const_cast<DiskManager*>(this)->updateDiskListFromMapping(mountPoint);

                    break;
                }
            }
        }
    }

    // 如果仍然没有找到已挂载的磁盘，返回空字符串
    if (result.empty()) {
        LogW("未找到已挂载的磁盘，返回空路径");
        return "";
    }
    
    // 根据使用场景，决定是否添加末尾斜杠
    // 对于大部分操作，我们需要确保路径末尾有斜杠
    return normalizePath(result);
}

// 设置活动磁盘路径（非阻塞版本）
bool DiskManager::setActiveDiskPath(const std::string& path)
{
    std::string oldPath = m_activeDiskPath;
    bool result = false;

    // 检查路径是否在磁盘列表中
    auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), path);
    if (it != m_diskPaths.end()) {
        m_activeDiskIndex = std::distance(m_diskPaths.begin(), it);
        m_activeDiskPath = path;
        LogI("已设置活动磁盘路径: %s (索引: %zu)", path.c_str(), m_activeDiskIndex);
        result = true;
    }
    // 如果路径不在列表中，但是存在且可访问，也允许设置
    else if (access(path.c_str(), F_OK) == 0) {
        m_activeDiskPath = path;
        LogW("设置的活动磁盘路径不在管理列表中: %s", path.c_str());
        result = true;
    }
    else {
        LogE("无法设置活动磁盘路径: %s (路径不存在或不可访问)", path.c_str());
        result = false;
    }

    // 磁盘切换回调功能已删除
    if (result && oldPath != path) {
        LogI("磁盘路径已切换: %s -> %s", oldPath.c_str(), path.c_str());
    }

    return result;
}

// 第三阶段：过度设计的接口已彻底删除

// 切换到指定磁盘
bool DiskManager::switchToDisk(const std::string& path)
{
    ScopedLocker lock(g_cleanupLock);
    
    // 首先检查是否有任何挂载的磁盘
    if (!hasAvailableDisk()) {
        LogE("没有可用的已挂载磁盘，无法切换磁盘");
        return false;
    }
    
    bool result = false;
    
    // 检查目标路径是否已挂载
    if (!isMounted(path)) {
        LogW("切换磁盘失败: 目标路径未挂载: %s", path.c_str());
        return false;
    }
    
    // 检查路径是否在列表中
    auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), path);
    if (it == m_diskPaths.end()) {
        LogW("路径不在管理列表中，尝试添加: %s", path.c_str());

        // 如果磁盘已挂载，将其添加到管理列表
        if (isMounted(path)) {
            // 🔥 USB路径分离：拒绝将USB设备路径添加到磁盘管理列表
            if (!isUsbDevicePath(path)) {
                m_diskPaths.push_back(path);
                it = m_diskPaths.end() - 1; // 指向新添加的元素
                LogI("成功将磁盘添加到管理列表: %s", path.c_str());
            } else {
                LogE("切换磁盘失败: 不允许切换到USB设备路径: %s", path.c_str());
                return false;
            }
        } else {
            LogW("切换磁盘失败: 路径不在管理列表中且未挂载: %s", path.c_str());
            return false;
        }
    }
    // 检查磁盘是否空间不足
    else if (isCriticalSpace(path)) {
        LogW("切换磁盘失败: 磁盘空间不足: %s", path.c_str());
    }
    else {
        // 更新活动磁盘索引和路径
        m_activeDiskIndex = std::distance(m_diskPaths.begin(), it);
        m_activeDiskPath = path;
        
        LogI("成功切换到磁盘: %s (索引: %zu)", path.c_str(), m_activeDiskIndex);
        result = true;

        // 磁盘切换成功后立即重置录像通道状态
        reset_all_channels_disk_switch_state();
    }
    
    return result;
}

// 更新所有磁盘状态
void DiskManager::updateAllDiskStatus()
{
    // 触发后台更新，避免阻塞调用者
    triggerDiskStatusUpdate();
}

// 获取所有磁盘信息
std::vector<DiskInfo> DiskManager::getAllDisksInfo()
{
    std::vector<DiskInfo> result;

    // 第二阶段优化：首先尝试从挂载缓存获取信息
    if (m_mountCache) {
        auto mountedDevices = m_mountCache->getAllMountedDevices();
        if (!mountedDevices.empty()) {
            // 从缓存构建DiskInfo
            for (const auto& mountInfo : mountedDevices) {
                DiskInfo info;
                info.path = mountInfo.mountPoint;
                info.device = mountInfo.device;
                info.uuid = mountInfo.uuid;
                info.fsType = mountInfo.fsType;
                info.isMounted = true;
                info.isRemovable = mountInfo.isRemovable;
                info.totalSpace = mountInfo.totalSpace;
                info.availSpace = mountInfo.availableSpace;
                info.lastChecked = mountInfo.lastChecked;
                info.status = 0;
                info.statusMessage = "正常";
                result.push_back(info);
            }

            LogI("从缓存获取磁盘信息，设备数量: %zu", result.size());
            return result;
        }
    }

    // 缓存未命中，使用TryLock避免阻塞
    if (m_diskInfoLock.TryLock()) {
        for (const auto& path : m_diskPaths) {
            if (m_diskInfoMap.find(path) != m_diskInfoMap.end()) {
                result.push_back(m_diskInfoMap[path]);
            }
        }
        m_diskInfoLock.Leave();

        // 如果缓存为空，触发后台更新
        if (result.empty()) {
            triggerDiskStatusUpdate();
        }
    } else {
        // 无法获取锁，触发后台更新并返回空结果
        LogW("获取磁盘信息时锁被占用，触发后台更新");
        triggerDiskStatusUpdate();
    }

    // 触发后台缓存刷新
    if (m_mountCache && m_mountCache->needsRefresh()) {
        m_mountCache->refreshCache();
    }

    return result;
}

// 触发后台磁盘状态更新（非阻塞）
void DiskManager::triggerDiskStatusUpdate()
{
    // 设置更新请求标志
    m_backgroundUpdateRequested = true;

    // 通知定期检查线程立即执行更新
    m_backgroundTaskCond.NotifyOne();
}



// === 日志控制接口实现 ===

// 设置静默模式（禁用磁盘相关日志）
void DiskManager::setSilentMode(bool enabled)
{
    m_silentMode.store(enabled);
    if (enabled) {
        LogI("磁盘管理器已启用静默模式，将减少日志输出");
    } else {
        LogI("磁盘管理器已禁用静默模式，恢复正常日志输出");
    }
}

// 检查是否为静默模式
bool DiskManager::isSilentMode() const
{
    return m_silentMode.load();
}

// 获取指定磁盘信息（第二阶段优化：优先从缓存查询）
DiskInfo DiskManager::getDiskInfo(const std::string& path)
{
    // 第二阶段优化：优先从缓存查询设备挂载状态
    if (m_mountCache) {
        // 检查是否是设备路径还是挂载点路径
        bool isDeviceMounted = m_mountCache->isDeviceMounted(path);
        if (isDeviceMounted) {
            std::string mountPoint = m_mountCache->getCacheDeviceMountPoint(path);
            DiskInfo info;
            info.path = mountPoint;
            info.device = path;
            info.isMounted = true;
            info.status = 0;
            info.statusMessage = "已挂载";
            info.lastChecked = time(NULL);
            LogI("从缓存获取设备信息: %s -> %s", path.c_str(), mountPoint.c_str());
            return info;
        }
    }

    // 缓存未命中，从磁盘信息映射查询
    // 如果磁盘在管理列表中，更新并返回信息
    if (m_diskInfoMap.find(path) != m_diskInfoMap.end()) {
        // 更新单个磁盘状态
        DiskInfo& info = m_diskInfoMap[path];
        info.lastChecked = time(NULL);

        // 检查挂载状态
        info.isMounted = isMounted(path);

        if (!info.isMounted) {
            info.status = 2; // 不可用
            info.statusMessage = "未挂载";
            info.availSpace = 0;
            info.totalSpace = 0;
            info.device = "";
            info.uuid = "";
            return info;
        }
		
        // 获取空间信息
        struct statvfs stat;
        if (statvfs(path.c_str(), &stat) == 0) {
            info.totalSpace = (uint64_t)stat.f_blocks * stat.f_frsize;
            info.availSpace = (uint64_t)stat.f_bavail * stat.f_frsize;
            
            // 计算剩余空间百分比
            double avail_percent = 0;
            if (stat.f_blocks > 0) {
                avail_percent = ((double)stat.f_bavail / stat.f_blocks) * 100.0;
            }
            
            // 设置状态
            if (avail_percent < 5.0) {
                info.status = 1; // 空间不足
                info.statusMessage = "空间严重不足";
            } else if (avail_percent < 10.0) {
                info.status = 1; // 空间不足
                info.statusMessage = "空间不足";
            } else {
                info.status = 0; // 正常
                info.statusMessage = "正常";
            }
        } else {
            info.status = 3; // 错误
            info.statusMessage = "无法获取磁盘信息";
        }
        
        // 获取设备路径和UUID
        char cmd[256];
        char buffer[128];
        memset(cmd, 0, sizeof(cmd));
        
        // 使用mount命令查找设备
        snprintf(cmd, sizeof(cmd), "mount | grep -w \"%s\" | awk '{print $1}'", path.c_str());
        FILE* fp = popen(cmd, "r");
        if (fp) {
            if (fgets(buffer, sizeof(buffer), fp)) {
                // 去除换行符
                size_t len = strlen(buffer);
                if (len > 0 && buffer[len - 1] == '\n') {
                    buffer[len - 1] = '\0';
                }
                info.device = buffer;
                
                // 获取UUID
                info.uuid = m_uuidManager->getDiskUUID(info.device);
            }
            pclose(fp);
        }
        
        return info;
    }

    if (access(path.c_str(), F_OK) == 0) {
        DiskInfo info;
        info.path = path;
        info.isMounted = true;
        info.lastChecked = time(NULL);
        
        // 获取空间信息
        struct statvfs stat;
        if (statvfs(path.c_str(), &stat) == 0) {
            info.totalSpace = (uint64_t)stat.f_blocks * stat.f_frsize;
            info.availSpace = (uint64_t)stat.f_bavail * stat.f_frsize;
            info.status = 0; // 正常
            info.statusMessage = "正常(未管理)";
        } else {
            info.totalSpace = 0;
            info.availSpace = 0;
            info.status = 3; // 错误
            info.statusMessage = "无法获取磁盘信息";
        }
        
        // 获取设备路径和UUID
        char cmd[256];
        char buffer[128];
        memset(cmd, 0, sizeof(cmd));
        
        // 使用mount命令查找设备
        snprintf(cmd, sizeof(cmd), "mount | grep -w \"%s\" | awk '{print $1}'", path.c_str());
        FILE* fp = popen(cmd, "r");
        if (fp) {
            if (fgets(buffer, sizeof(buffer), fp)) {
                // 去除换行符
                size_t len = strlen(buffer);
                if (len > 0 && buffer[len - 1] == '\n') {
                    buffer[len - 1] = '\0';
                }
                info.device = buffer;
                
                // 获取UUID
                info.uuid = m_uuidManager->getDiskUUID(info.device);
            }
            pclose(fp);
        }
        
        return info;
    }
    
    // 如果路径不存在，返回错误信息
    DiskInfo info;
	info.path = path;
	info.isMounted = false;
	info.totalSpace = 0;
	info.availSpace = 0;
    info.lastChecked = time(NULL);
    info.status = 2; // 不可用
    info.statusMessage = "路径不存在";
    info.device = "";
    info.uuid = "";
    
    return info;
}


void removeTrailingSlash(std::string& path) 
{
    if (!path.empty() && path.back() == '/') {
        path.pop_back();
    }
}


bool DiskManager::isMounted(const std::string& path) const
{
    bool mounted = false;
	
    if (!path.empty()) {
        // 创建不带末尾斜杠的路径副本
        std::string pathNoSlash = path;
        removeTrailingSlash(pathNoSlash);
        
        // 直接读取 /proc/mounts 文件检查挂载状态
        FILE* fp = fopen("/proc/mounts", "r");
        if (!fp) {
            LogE("无法打开 /proc/mounts 文件");
            return false;
        }
        
        char line[512];
        
        // 检查两种可能的路径格式：带斜杠和不带斜杠
        while (fgets(line, sizeof(line), fp) != NULL) {
            // 去掉换行符
            char* newline = strchr(line, '\n');
            if (newline) *newline = '\0';
            
            // 使用不带末尾斜杠的路径进行比较
            if (strstr(line, pathNoSlash.c_str())) {
                mounted = true;
                break;
            }
            
            // 如果不带斜杠的比较失败，再尝试带斜杠的原始路径
            if (!mounted && strstr(line, path.c_str())) {
                mounted = true;
                break;
            }
        }
        fclose(fp);
        
        if (!mounted) {
            // 减少日志输出频率
            static time_t last_log_time = 0;
            time_t now = time(NULL);
            if (now - last_log_time > 60) { // 每分钟最多输出一次
                LogW("磁盘未挂载: %s", pathNoSlash.c_str());
                last_log_time = now;
            }
            return false;
        }
        
        return true;
    }
    
    return mounted;
}

// 检查设备是否已挂载到任何位置
bool DiskManager::isDeviceAlreadyMounted(const std::string& devicePath) const
{
    FILE* fp = fopen("/proc/mounts", "r");
    if (!fp) {
        LogE("无法打开 /proc/mounts");
        return false;
    }

    char line[1024];
    bool isMounted = false;

    while (fgets(line, sizeof(line), fp)) {
        char device[256], mountpoint[256], fstype[64], options[256];
        int fields = sscanf(line, "%255s %255s %63s %255s", device, mountpoint, fstype, options);

        if (fields >= 2) {
            // 比较设备路径
            if (strcmp(device, devicePath.c_str()) == 0) {
                LogI("设备 %s 已挂载到: %s", devicePath.c_str(), mountpoint);
                isMounted = true;
                break;
            }
        }
    }

    fclose(fp);
    return isMounted;
}

// 获取设备当前挂载点
std::string DiskManager::getDeviceMountPoint(const std::string& devicePath) const
{
    FILE* fp = fopen("/proc/mounts", "r");
    if (!fp) {
        LogE("无法打开 /proc/mounts");
        return "";
    }

    char line[1024];
    std::string mountPoint = "";

    while (fgets(line, sizeof(line), fp)) {
        char device[256], mountpoint[256], fstype[64], options[256];
        int fields = sscanf(line, "%255s %255s %63s %255s", device, mountpoint, fstype, options);

        if (fields >= 2) {
            // 比较设备路径
            if (strcmp(device, devicePath.c_str()) == 0) {
                mountPoint = mountpoint;
                LogI("设备 %s 当前挂载点: %s", devicePath.c_str(), mountpoint);
                break;
            }
        }
    }

    fclose(fp);
    return mountPoint;
}

// ========== 统一磁盘空间检测接口实现 ==========

// 获取磁盘使用百分比（统一接口，带缓存机制）
double DiskManager::getDiskUsagePercent(const std::string& path, bool force_check)
{
    // 缓存结构
    struct DiskUsageCache {
        double usagePercent;
        time_t lastCheckTime;
        std::string path;
    };

    static std::map<std::string, DiskUsageCache> usageCache;
    static TCSLock cacheLock;

    time_t now = time(NULL);
    std::string normPath = normalizePath(path);

    // 检查缓存
    if (!force_check) {
        ScopedLocker lock(cacheLock);
        auto it = usageCache.find(normPath);
        if (it != usageCache.end() &&
            (now - it->second.lastCheckTime) < DISK_CLEANUP_CACHE_TIMEOUT_SECONDS) {
            return it->second.usagePercent;
        }
    }

    // 执行实际检测
    double usagePercent = 100.0; // 默认假设磁盘已满

    if (!normPath.empty()) {
        struct statvfs stat;
        if (statvfs(normPath.c_str(), &stat) == 0) {
            double avail = stat.f_bavail;
            double total = stat.f_blocks;
            if (total > 0) {
                usagePercent = 100.0 - (avail / total) * 100.0;
            }
        } else {
            LogW("无法获取磁盘状态: %s, errno=%d", normPath.c_str(), errno);
        }
    } else {
        LogW("磁盘路径为空，假设磁盘已满");
    }

    // 更新缓存
    {
        ScopedLocker lock(cacheLock);
        DiskUsageCache& cache = usageCache[normPath];
        cache.usagePercent = usagePercent;
        cache.lastCheckTime = now;
        cache.path = normPath;
    }

	LogI("path = %s, per = %.2f", path.c_str(), usagePercent);

    return usagePercent;
}

// 统一清盘触发检查（内部使用）
void DiskManager::checkAndTriggerCleanupIfNeeded(const std::string& path, const char* trigger_source)
{
    if (path.empty()) {
		LogW("path 为空 ");
        return;
    }

    // 检查是否已经在清理中
    if (isCleanupInProgress()) {
		LogW("已有清盘任务!! 丢掉本次!!");
        return;
    }

    double usagePercent = getDiskUsagePercent(path, false);

    // 根据统一阈值判断是否需要清盘
    if (usagePercent >= DISK_CRITICAL_THRESHOLD_PERCENT) {
        // 严重状态：立即触发大容量清盘
        static time_t lastCriticalCleanup = 0;
        time_t now = time(NULL);
		time_t cha = now - lastCriticalCleanup;
        if (cha > DISK_CRITICAL_WAIT) { // 10秒防重复
            LogE("[%s] 磁盘空间严重不足(%.2f%%)，触发紧急清盘: %s",
                 trigger_source, usagePercent, path.c_str());
            uint64_t bytesToFree = 2048 * 1024 * 1024; // 释放2GB空间
            requestAsyncCleanup(0, bytesToFree);
            lastCriticalCleanup = now;
        }
		else{
			LogI("严重不足: usagePercent = %lf, 等待: %d", usagePercent, cha);
		}
    } 
	else if (usagePercent >= DISK_CLEANUP_THRESHOLD_PERCENT) {
        // 清盘阈值：触发标准清盘
        static time_t lastStandardCleanup = 0;
        time_t now = time(NULL);
		time_t cha = now - lastStandardCleanup;
        if (cha > DISK_CLEANUP_WAIT) { // 20s防重复
            LogW("[%s] 磁盘空间不足(%.2f%%)，触发清盘: %s",
                 trigger_source, usagePercent, path.c_str());
            uint64_t bytesToFree = 1024 * 1024 * 1024; // 释放1GB空间
            requestAsyncCleanup(0, bytesToFree);
            lastStandardCleanup = now;
        }
		else{
			LogI("标准清理: usagePercent = %lf, 等待: %d", usagePercent, cha);
		}
    } 
	else if (usagePercent >= DISK_WARNING_THRESHOLD_PERCENT) {
        // 90%阈值：触发磁盘切换检查
        static time_t lastSwitchCheck = 0;
        time_t now = time(NULL);
		time_t cha = now - lastSwitchCheck;
        if (cha > 30) { // 30秒检查一次磁盘切换
            LogW("[%s] 磁盘空间达到切换阈值(%.2f%%)，检查磁盘切换: %s",
                 trigger_source, usagePercent, path.c_str());

            // 记录切换前的磁盘路径
            std::string originalPath = path;

            // 触发磁盘切换检查
            DiskManager* diskManager = DiskManager::getInstance();
            if (diskManager) {
                bool switched = diskManager->checkAndSwitchDiskIfNeeded();

                // 检查磁盘是否真正切换了
                std::string newPath = diskManager->getActiveDiskPath();
                bool actuallyChanged = (normalizePath(originalPath) != normalizePath(newPath));

                if (switched && actuallyChanged) {
                    LogI("磁盘切换成功: %s -> %s", originalPath.c_str(), newPath.c_str());
                    // 切换成功，重置计时器，避免在新磁盘上立即重复检查
                    lastSwitchCheck = now;
                } else if (switched && !actuallyChanged) {
                    LogW("磁盘切换返回成功但实际未切换，可能是2小时等待机制生效");
                    // 虽然返回成功但未实际切换，延长等待时间
                    lastSwitchCheck = now;
                } else {
                    LogW("磁盘切换失败或无需切换，当前磁盘: %s", path.c_str());
                    // 切换失败，延长等待时间，避免频繁重试
                    lastSwitchCheck = now;
                }
            } else {
                lastSwitchCheck = now;
            }
        }
		else{
			// 减少频繁的调试日志，只在特定条件下打印
			if (cha % 10 == 0) { // 每10秒打印一次状态
				LogI("切换检查等待中: 使用率=%.2f%%, 剩余等待=%d秒", usagePercent, 30 - cha);
			}
		}
    }

}

bool DiskManager::isCriticalSpace(const std::string& path)
{
    if (path.empty()) {
        return true;
    }

    // 使用统一接口获取磁盘使用率
    double usagePercent = getDiskUsagePercent(path, false);

    // 注意：不在这里调用 checkAndTriggerCleanupIfNeeded，避免递归调用
    // isCriticalSpace 主要用于状态检查，不应该触发切换逻辑

    // 使用严重空间不足阈值（98%）
    return usagePercent >= DISK_CRITICAL_THRESHOLD_PERCENT;
}

bool DiskManager::switchToNextDisk()
{
    ScopedLocker lock(g_cleanupLock);
    bool result = false;
    
    // 录像文件完整性保护：确保当前录像文件完整关闭
    LogI("磁盘切换:: 执行录像文件完整性保护");
    notifyDiskSwitchPending();
    waitForRecordingComplete(15); // 等待15秒

    // 记录当前活动磁盘路径
    std::string currentDiskPath = m_diskPaths.empty() ? "" : m_diskPaths[m_activeDiskIndex];
    
    // 如果路径列表为空，无法切换
    if (m_diskPaths.empty()) {
        LogW("磁盘切换:: 磁盘路径列表为空，无法切换");
        return false;
    }
    
    // 如果只有一个磁盘，无法切换
    if (m_diskPaths.size() == 1) {
        LogW("磁盘切换:: 只有一个磁盘可用，无法切换，执行清盘操作");
        uint64_t bytesToFree = 512 * 1024 * 1024; // 尝试释放512MB空间
        requestAsyncCleanup(0, bytesToFree);
        return false;
    }
    
    // 避免重复刷新挂载点，使用缓存优化
    static time_t last_refresh_time = 0;
    time_t now = time(NULL);
    const int REFRESH_INTERVAL = 10; // 10秒刷新一次
    
    // 检查是否需要刷新挂载点列表
    bool need_refresh = (now - last_refresh_time >= REFRESH_INTERVAL);
    
    if (need_refresh) {
        LogI("磁盘切换:: 刷新磁盘挂载状态...");
        
        // 获取DISK_MAPPING中定义的所有有效挂载点
        MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
        std::set<std::string> validMountPoints;
        
        // 收集所有来自DISK_MAPPING的挂载点
        for (int i = 0; i < MAX_DISK_COUNT; i++) {
            if (disks[i].mountpoint && *disks[i].mountpoint) {
                std::string mountPoint = disks[i].mountpoint;
                
                // 确保路径末尾有斜杠
                if (!mountPoint.empty() && mountPoint.back() != '/') {
                    mountPoint += '/';
                }
                
                validMountPoints.insert(mountPoint);
            }
        }
        
        // 使用set存储唯一的磁盘路径
        std::set<std::string> uniquePaths;
        
        // 先检测并添加DISK_MAPPING中已挂载的磁盘，未挂载的尝试挂载
        for (const auto& mountPoint : validMountPoints) {
            bool mounted = isMounted(mountPoint);

            // 如果未挂载，尝试挂载
            if (!mounted) {
                LogI("磁盘切换:: 发现未挂载的磁盘，尝试挂载: %s", mountPoint.c_str());

                // 从挂载点推断设备路径（使用DISK_MAPPING）
                std::string devicePath = findDeviceByMountPath(mountPoint);
                if (devicePath.empty()) {
                    LogW("磁盘切换:: 无法推断设备路径，跳过: %s", mountPoint.c_str());
                    continue;
                }

                // 尝试挂载
                bool mountSuccess = mountDiskInternal(devicePath);
                if (mountSuccess) {
                    LogI("磁盘切换:: 挂载成功: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                    mounted = true;
                } else {
                    LogW("磁盘切换:: 挂载失败: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                }
            }

            // 只有挂载成功的磁盘才加入轮转列表
            if (mounted) {
                uniquePaths.insert(mountPoint);
                LogI("磁盘已加入轮转列表: %s", mountPoint.c_str());
            } else {
                LogW("磁盘未挂载，不加入轮转列表: %s", mountPoint.c_str());
            }
        }
        
        // 更新磁盘路径列表
        if (uniquePaths.size() != m_diskPaths.size() || 
            !std::equal(uniquePaths.begin(), uniquePaths.end(), m_diskPaths.begin())) {
            
            LogI("磁盘列表已更新，重置切换尝试次数");
            m_diskPaths.clear();
            for (const auto& path : uniquePaths) {
                // USB路径分离：只添加非USB设备路径
                if (!isUsbDevicePath(path)) {
                    m_diskPaths.push_back(path);
                } else {
                    LogI("跳过USB设备路径: %s", path.c_str());
                }
            }
            
            // 如果当前活动磁盘不在新列表中，重置索引
            auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), currentDiskPath);
            if (it != m_diskPaths.end()) {
                m_activeDiskIndex = std::distance(m_diskPaths.begin(), it);
            } else {
                m_activeDiskIndex = 0;
            }
            
            // 磁盘切换尝试次数变量已删除
        }
        
        last_refresh_time = now;
    }
    
    // 更新当前活动磁盘路径（可能在刷新过程中已更新）
    currentDiskPath = m_diskPaths.empty() ? "" : m_diskPaths[m_activeDiskIndex];
    
    // 处理切换逻辑
    if (m_diskPaths.empty()) {
        LogW("没有可用的磁盘路径");
        return false;
    }
    // 磁盘切换尝试次数变量已删除，直接尝试切换
    else {
        // 保存原始磁盘索引和路径，以便在找不到合适磁盘时恢复
        size_t originalIndex = m_activeDiskIndex;
        std::string originalPath = currentDiskPath;
        bool found = false;

        LogW("当前磁盘: %s (索引:%zu), 尝试切换到其他磁盘",
             currentDiskPath.c_str(), originalIndex);
        
        // 遍历所有磁盘路径，检查每个磁盘是否可用
        for (size_t i = 0; i < m_diskPaths.size(); i++) {
            // 计算下一个要检查的磁盘索引，跳过当前磁盘
            size_t nextIndex = (originalIndex + i + 1) % m_diskPaths.size();
            
            // 如果遍历回到原始索引，说明已经检查了所有磁盘
            if (nextIndex == originalIndex) {
                continue;
            }
            
            // 获取磁盘路径
            std::string diskPath = m_diskPaths[nextIndex];
            
            // 规范化路径格式进行比较
            std::string normDiskPath = normalizePath(diskPath);
            std::string normCurrentPath = normalizePath(currentDiskPath);
            std::string normOriginalPath = normalizePath(originalPath);
            
            // 确保不切换到相同的磁盘
            if (normDiskPath == normCurrentPath || normDiskPath == normOriginalPath) {
                LogI("跳过相同磁盘路径: %s", diskPath.c_str());
                continue;
            }
            
            bool mounted = isMounted(diskPath);

            // 如果磁盘未挂载，尝试挂载
            if (!mounted) {
                LogI("磁盘未挂载，尝试挂载: %s", diskPath.c_str());

                // 从磁盘路径推断设备路径（使用DISK_MAPPING）
                std::string devicePath = findDeviceByMountPath(diskPath);
                if (devicePath.empty()) {
                    LogW("无法推断设备路径，跳过挂载: %s", diskPath.c_str());
                    continue;
                }

                // 尝试挂载磁盘
                bool mountSuccess = mountDiskInternal(devicePath);
                if (mountSuccess) {
                    LogI("挂载成功: %s -> %s", devicePath.c_str(), diskPath.c_str());
                    mounted = true;
                } else {
                    LogW("挂载失败: %s -> %s", devicePath.c_str(), diskPath.c_str());
                    continue;
                }
            }

            bool critical = isCriticalSpace(diskPath);

            // 只打印简要的磁盘信息，减少日志量
            if (mounted) {
                struct statvfs stat;
                if (statvfs(diskPath.c_str(), &stat) == 0) {
                    double avail = stat.f_bavail;
                    double total = stat.f_blocks;
                    double percent = (avail / total) * 100.0;

                    LogI("检查磁盘: %s (索引:%zu) - %s, 可用:%.2f%%",
                         diskPath.c_str(), nextIndex,
                         critical ? "空间不足" : "空间充足", percent);
                }
            } else {
                LogI("检查磁盘: %s (索引:%zu) - 挂载失败", diskPath.c_str(), nextIndex);
            }

            // 检查磁盘是否可用（必须已挂载且空间充足）
            if (mounted && !critical) {
                // 找到可用磁盘，设置为活动磁盘
                m_activeDiskIndex = nextIndex;
                m_activeDiskPath = normalizePath(diskPath);  // 同时更新活动磁盘路径，并确保格式一致
                found = true;
                // 磁盘切换尝试次数变量已删除
                LogW("已找到可用磁盘: %s (索引:%zu)", diskPath.c_str(), nextIndex);
                break;
            }
        }
        
        // 如果没有找到可用磁盘，回到原始磁盘
        if (!found) {
            LogW("没有找到可用的备用磁盘，保持原磁盘: %s", currentDiskPath.c_str());
            m_activeDiskIndex = originalIndex;
            // 确保m_activeDiskPath与m_activeDiskIndex一致
            if (originalIndex < m_diskPaths.size()) {
                m_activeDiskPath = m_diskPaths[originalIndex];
            }
            
            // 磁盘切换尝试次数变量已删除，直接请求清盘
            LogW("没有找到可用磁盘，在当前磁盘执行清盘");
            // 请求在当前磁盘上执行清盘操作
            uint64_t bytesToFree = 512 * 1024 * 1024; // 尝试释放512MB空间
            requestAsyncCleanup(0, bytesToFree);
        } 
        else {
            // 打印最终的活动磁盘信息
            std::string finalPath = getActiveDiskPath();
            
            // 规范化路径比较
            std::string normFinalPath = normalizePath(finalPath);
            std::string normCurrentPath = normalizePath(currentDiskPath);
            
            // 确保不是自我切换
            if (normFinalPath == normCurrentPath) {
                LogW("检测到自我切换，保持原磁盘: %s", currentDiskPath.c_str());
                
                // 重置为原始磁盘
                m_activeDiskIndex = originalIndex;
                m_activeDiskPath = originalPath;
                return false;
            }
            
            LogW("成功切换到磁盘: %s (索引:%zu)", finalPath.c_str(), m_activeDiskIndex);

            // 磁盘切换回调功能已删除
            LogI("磁盘路径已更改: %s -> %s", currentDiskPath.c_str(), m_activeDiskPath.c_str());

            // 切换到新磁盘后，检查新磁盘是否需要清理
            double newDiskUsage = getDiskUsagePercent(finalPath, true);
            LogI("新磁盘使用率: %.2f%%", newDiskUsage);

            if (newDiskUsage >= DISK_CLEANUP_THRESHOLD_PERCENT) {
                LogW("新磁盘空间不足(%.2f%%)，立即执行清理", newDiskUsage);
                uint64_t bytesToFree = 1024 * 1024 * 1024; // 释放1GB空间
                requestAsyncCleanup(0, bytesToFree);
            } else {
                LogI("新磁盘空间充足(%.2f%%)，无需清理", newDiskUsage);
            }

            result = true;
        }
    }
    
    return result;
}

void DiskManager::cleanupSpace(uint64_t bytesToFree)
{
    // 使用ScopedLocker来获取锁，离开作用域时自动释放
    ScopedLocker lock(g_cleanupLock);
    
    // 清理空间
    LogW("尝试全局清理 %llu MB 空间", (unsigned long long)bytesToFree/1024/1024);
    
    // 寻找最旧的录像文件进行删除
    std::string recordPath = getActiveDiskPath();
    
    // 首先检查磁盘空间是否已经足够
    if (!isCriticalSpace(recordPath)) {
        LogW("磁盘空间已经足够，无需执行全局清盘");
        return;
    }
    
    DIR* dir = opendir(recordPath.c_str());
    if (!dir) {
        LogE("无法打开录像目录: %s", recordPath.c_str());
        return;
    }
    
    // 找所有日期目录（按新格式：/mnt/custom/disk/disk1/YYYYMMDD）
    std::vector<std::string> dateDirs;
    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_DIR && 
            strlen(entry->d_name) == 8 && 
            isdigit(entry->d_name[0])) {
            dateDirs.push_back(entry->d_name);
        }
    }
    closedir(dir);
    
    // 没有找到任何日期目录
    if (dateDirs.empty()) {
        LogW("没有找到任何日期目录");
        return;
    }
    
    // 按照字母顺序排序（对于YYYYMMDD格式的文件夹，最旧的排在前面）
    std::sort(dateDirs.begin(), dateDirs.end());
    
    // 获取当前的时间和日期
    time_t now = time(NULL);
	struct tm *now_tm = gmtime(&now);
    
    char today_str[9];
    snprintf(today_str, sizeof(today_str), "%04d%02d%02d", 
             now_tm->tm_year+1900, now_tm->tm_mon+1, now_tm->tm_mday);
    
    uint64_t total_freed = 0;
    bool success = false;
    
    // 步骤1: 清理旧录像文件（非当天录像）
    LogW("步骤1: 尝试清理旧录像");
    for (const auto& date_dir : dateDirs) {
        // 跳过今天的目录
        if (date_dir == today_str) {
            continue;
        }
        
        std::string oldestDir = recordPath + "/" + date_dir;
        LogW("处理旧日期目录: %s", oldestDir.c_str());
        
        // 遍历日期目录下的所有通道目录
        DIR* dateDir = opendir(oldestDir.c_str());
        if (!dateDir) {
            continue;
        }
        
        struct dirent* channelEntry;
        std::vector<std::string> channels;
        
        // 收集所有通道目录
        while ((channelEntry = readdir(dateDir)) != NULL) {
            if (strcmp(channelEntry->d_name, ".") == 0 || strcmp(channelEntry->d_name, "..") == 0) {
                continue;
            }
            
            std::string channelPath = oldestDir + "/" + channelEntry->d_name;
            struct stat st;
            if (stat(channelPath.c_str(), &st) == 0 && S_ISDIR(st.st_mode)) {
                channels.push_back(channelPath);
            }
        }
        closedir(dateDir);
        
        // 按照通道名排序
        std::sort(channels.begin(), channels.end());
        
        // 清理每个通道目录
        for (const auto& channelPath : channels) {
            // 清理通道目录中的文件
            if (cleanupDirectoryByFiles(channelPath, total_freed, bytesToFree, recordPath)) {
                LogW("通过清理旧录像达到目标: %s", channelPath.c_str());
                success = true;
                break;
            }
            
            // 每清理一个通道目录后检查磁盘空间状态
            if (!isCriticalSpace(recordPath)) {
                LogW("全局清盘: 磁盘空间已足够，提前结束");
                success = true;
                break;
            }
        }
        
        if (success) {
            break;
        }
        
        // 尝试删除日期目录（如果为空）
        bool is_empty = true;
        DIR* check_dir = opendir(oldestDir.c_str());
        if (check_dir) {
            struct dirent* check_entry;
            while ((check_entry = readdir(check_dir)) != NULL) {
                // 跳过 . 和 ..
                if (strcmp(check_entry->d_name, ".") == 0 || strcmp(check_entry->d_name, "..") == 0) {
                    continue;
                }
                
                // 目录不为空
                is_empty = false;
                break;
            }
            closedir(check_dir);
            
            // 只有当目录为空时才尝试删除
            if (is_empty) {
                if (rmdir(oldestDir.c_str()) == 0) {
                    LogW("删除空目录成功: %s", oldestDir.c_str());
                } else {
                    LogE("删除空目录失败: %s, errno=%d", oldestDir.c_str(), errno);
                }
            }
        }
    }
    
    // 步骤2: 如果前面的步骤不够，才清理当天录像
    if (!success) {
        LogW("步骤2: 尝试清理当天录像");
        // 找到当天目录
        for (const auto& date_dir : dateDirs) {
            if (date_dir == today_str) {
                std::string todayDir = recordPath + "/" + date_dir;
                LogW("处理当天目录: %s", todayDir.c_str());
                
                // 遍历当天目录下的所有通道
                DIR* todayDirHandle = opendir(todayDir.c_str());
                if (!todayDirHandle) {
                    LogE("无法打开当天目录: %s", todayDir.c_str());
                    break;
                }
                
                struct dirent* channelEntry;
                std::vector<std::string> channels;
                
                while ((channelEntry = readdir(todayDirHandle)) != NULL) {
                    if (strcmp(channelEntry->d_name, ".") == 0 || strcmp(channelEntry->d_name, "..") == 0) {
                        continue;
                    }
                    
                    std::string channelPath = todayDir + "/" + channelEntry->d_name;
                    struct stat st;
                    if (stat(channelPath.c_str(), &st) == 0 && S_ISDIR(st.st_mode)) {
                        channels.push_back(channelPath);
                    }
                }
                closedir(todayDirHandle);
                
                // 按照通道名排序
                std::sort(channels.begin(), channels.end());
                
                // 清理每个通道目录
                for (const auto& channelPath : channels) {
                    // 清理通道目录中的文件
                    if (cleanupDirectoryByFiles(channelPath, total_freed, bytesToFree, recordPath)) {
                        LogW("通过清理当天录像达到目标: %s", channelPath.c_str());
                        success = true;
                        break;
                    }
                }
                
                break;
            }
        }
    }
    
    LogW("全局清理完成: 释放空间=%llu MB, 目标=%llu MB, 结果=%s", 
         (unsigned long long)(total_freed/1024/1024), 
         (unsigned long long)(bytesToFree/1024/1024),
         success ? "成功" : "未达目标");
}

// 按通道清理过期录像
bool DiskManager::cleanupChannelSpace(int channel_id, uint64_t bytesToFree, const std::string& path)
{
    // 使用ScopedLocker来获取锁，离开作用域时自动释放
    ScopedLocker lock(g_cleanupLock);
    
    LogW("尝试按通道清理空间: 通道=%d, 目标=%llu MB", 
         channel_id, (unsigned long long)bytesToFree/1024/1024);
    
    std::string basePath = path;
    if (basePath.empty()) {
        basePath = getActiveDiskPath();
    }
    
    // 首先检查磁盘空间是否已经足够
    if (!isCriticalSpace(basePath)) {
        LogW("磁盘空间已经足够，无需执行通道清盘: 通道=%d", channel_id);
        return true;
    }
    
    // 检查路径是否存在
    struct stat st;
    if (stat(basePath.c_str(), &st) != 0 || !S_ISDIR(st.st_mode)) {
        LogE("路径不存在或不是目录: %s", basePath.c_str());
        return false;
    }
    
    // 查找所有日期目录
    DIR* dir = opendir(basePath.c_str());
    if (!dir) {
        LogE("无法打开目录: %s", basePath.c_str());
        return false;
    }
    
    std::vector<std::string> date_dirs;
    struct dirent* date_entry;
    
    // 收集所有YYYYMMDD格式的日期目录
    while ((date_entry = readdir(dir)) != NULL) {
        if (date_entry->d_type == DT_DIR && 
            strlen(date_entry->d_name) == 8 && 
            isdigit(date_entry->d_name[0])) {
            // 转换为日期，验证这是一个有效的日期目录
            struct tm tm_val;
            memset(&tm_val, 0, sizeof(tm_val));
            char date_str[9];
            strncpy(date_str, date_entry->d_name, 8);
            date_str[8] = '\0';
            
            if (sscanf(date_str, "%4d%2d%2d", 
                      &tm_val.tm_year, &tm_val.tm_mon, &tm_val.tm_mday) == 3) {
                // 调整年份和月份
                tm_val.tm_year -= 1900;
                tm_val.tm_mon -= 1;
                
                // 确保这是一个有效的日期
                if (tm_val.tm_mon >= 0 && tm_val.tm_mon < 12 && 
                    tm_val.tm_mday >= 1 && tm_val.tm_mday <= 31) {
                    date_dirs.push_back(date_entry->d_name);
                }
            }
        }
    }
    closedir(dir);
    
    // 按照字母顺序排序（对于YYYYMMDD格式的文件夹，最旧的排在前面）
    std::sort(date_dirs.begin(), date_dirs.end());
    
    // 获取当前的时间和日期
    time_t now = time(NULL);
	struct tm *now_tm = gmtime(&now);
    
    char today_str[9];
    snprintf(today_str, sizeof(today_str), "%04d%02d%02d", 
             now_tm->tm_year+1900, now_tm->tm_mon+1, now_tm->tm_mday);
    
    uint64_t total_freed = 0;
    bool success = false;
    
    // 步骤1: 先尝试清理非当前通道的旧录像（优先清理最早的日期目录）
    LogW("步骤1: 尝试清理非当前通道的旧录像");
    for (const auto& date_dir : date_dirs) {
        // 跳过今天的目录
        if (date_dir == today_str) {
            continue;
        }
        
        // 遍历该日期下的所有通道目录
        std::string date_path = basePath + "/" + date_dir;
        DIR* chan_dir = opendir(date_path.c_str());
        if (!chan_dir) {
            continue;
        }
        
        struct dirent* chan_entry;
        std::vector<std::string> other_channels;
        
        while ((chan_entry = readdir(chan_dir)) != NULL) {
            if (strcmp(chan_entry->d_name, ".") == 0 || strcmp(chan_entry->d_name, "..") == 0) {
                continue;
            }
            
            // 跳过当前通道
            int entry_channel_id = atoi(chan_entry->d_name);
            if (entry_channel_id == channel_id) {
                continue;
            }
            
            std::string channel_path = date_path + "/" + chan_entry->d_name;
            struct stat chan_st;
            if (stat(channel_path.c_str(), &chan_st) == 0 && S_ISDIR(chan_st.st_mode)) {
                other_channels.push_back(channel_path);
            }
        }
        closedir(chan_dir);
        
        // 按照目录名排序，保持一定的清理顺序
        std::sort(other_channels.begin(), other_channels.end());
        
        // 清理找到的其他通道的录像
        for (const auto& other_channel_path : other_channels) {
            if (cleanupDirectoryByFiles(other_channel_path, total_freed, bytesToFree, basePath)) {
                LogW("通过清理其他通道的旧录像达到目标: %s", other_channel_path.c_str());
                success = true;
                break;
            }
            
            // 每清理一个通道目录后检查磁盘空间状态
            if (!isCriticalSpace(basePath)) {
                LogW("通道清盘: 磁盘空间已足够，提前结束: 通道=%d", channel_id);
                success = true;
                break;
            }
        }
        
        if (success) {
            break;
        }
    }
    
    // 步骤2: 如果步骤1没有释放足够空间，清理当前通道的非当天录像
    if (!success) {
        LogW("步骤2: 尝试清理当前通道的非当天录像");
        for (const auto& date_dir : date_dirs) {
            // 跳过今天的目录
            if (date_dir == today_str) {
                continue;
            }
            
            std::string channel_path = basePath + "/" + date_dir + "/" + std::to_string(channel_id);
            
            // 检查通道目录是否存在
            if (stat(channel_path.c_str(), &st) == 0 && S_ISDIR(st.st_mode)) {
                if (cleanupDirectoryByFiles(channel_path, total_freed, bytesToFree, basePath)) {
                    LogW("通过清理当前通道的旧录像达到目标: %s", channel_path.c_str());
                    success = true;
                    break;
                }
                
                // 检查磁盘空间状态
                if (!isCriticalSpace(basePath)) {
                    LogW("通道清盘步骤2: 磁盘空间已足够，提前结束: 通道=%d", channel_id);
                    success = true;
                    break;
                }
            }
        }
    }
    
    // 步骤3: 最后才尝试清理当天录像（如果前两步都不够的情况下）
    if (!success) {
        LogW("步骤3: 尝试清理当天录像");
        // 找到当天的目录
        for (const auto& date_dir : date_dirs) {
            if (date_dir == today_str) {
                std::string channel_path = basePath + "/" + date_dir + "/" + std::to_string(channel_id);
                
                // 检查通道目录是否存在
                if (stat(channel_path.c_str(), &st) == 0 && S_ISDIR(st.st_mode)) {
                    if (cleanupDirectoryByFiles(channel_path, total_freed, bytesToFree, basePath)) {
                        LogW("通过清理当天录像达到目标: %s", channel_path.c_str());
                        success = true;
                    }
                    
                    // 检查磁盘空间状态
                    if (!isCriticalSpace(basePath)) {
                        LogW("通道清盘步骤3: 磁盘空间已足够，提前结束: 通道=%d", channel_id);
                        success = true;
                    }
                }
                break;
            }
        }
    }
    
    LogW("按通道清理完成: 通道=%d, 释放空间=%llu MB, 目标=%llu MB, 结果=%s", 
         channel_id, 
         (unsigned long long)(total_freed/1024/1024), 
         (unsigned long long)(bytesToFree/1024/1024),
         success ? "成功" : "未达目标");
    
    return success;
}


/**
 * 尝试删除空目录（index.db文件被视为可忽略的系统文件）
 *
 * 此函数用于统一处理空目录的删除逻辑，避免代码重复。
 * index.db文件被视为录像系统的索引文件，不影响空目录的判断。
 *
 * @param dir_path 要检查和删除的目录路径
 *
 * @return bool 删除结果
 *              true: 目录被成功删除
 *              false: 目录未被删除（不符合删除条件或删除操作失败）
 *
 * @note 空目录的定义：
 *       - 完全空的目录（不包含任何文件和子目录）
 *       - 只包含index.db文件的目录（index.db被视为系统文件，可忽略）
 *
 * @note 函数执行流程：
 *       1. 打开并遍历目录，检查所有文件和子目录
 *       2. 跳过特殊目录项（. 和 ..）
 *       3. 遇到index.db文件时，记录但不影响空目录判断
 *       4. 遇到其他任何文件或子目录时，认为目录非空
 *       5. 如果目录符合删除条件：
 *          - 先删除index.db文件（如果存在）
 *          - 然后删除空目录
 *       6. 记录详细的操作日志和错误信息
 *
 * @warning 此函数会实际删除文件和目录，调用前请确保路径正确
 */
bool DiskManager::tryDeleteEmptyDirectory(const std::string& dir_path)
{
    LogWF("检查目录是否可删除（index.db被视为可忽略文件）: %s", dir_path.c_str());

    bool can_delete_dir = true;
    bool has_index_db = false;
    std::string index_db_path;

    // 打开目录进行内容检查
    DIR* check_dir = opendir(dir_path.c_str());
    if (!check_dir) {
        LogE("无法打开目录进行删除检查: %s, errno=%d", dir_path.c_str(), errno);
        return false;
    }

    // 遍历目录内容
    struct dirent* entry;
    while ((entry = readdir(check_dir)) != NULL) {
        // 跳过特殊目录项
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        // 检查是否是index.db文件
        if (strcmp(entry->d_name, "index.db") == 0) {
            // index.db文件被视为系统文件，不影响空目录判断
            has_index_db = true;
            index_db_path = dir_path + "/index.db";
            LogWF("发现index.db文件: %s", index_db_path.c_str());
            continue;  // 跳过，不影响空目录判断
        } else {
            // 发现其他文件或子目录，认为目录非空
            LogWF("目录包含其他文件/目录，不符合删除条件: %s -> %s", dir_path.c_str(), entry->d_name);
            can_delete_dir = false;
            break;
        }
    }
    closedir(check_dir);

    // 如果不符合删除条件，直接返回
    if (!can_delete_dir) {
        return false;
    }

    // 执行删除操作
    bool delete_success = true;

    // 先删除index.db文件（如果存在）
    if (has_index_db) {
        if (unlink(index_db_path.c_str()) == 0) {
            LogWF("成功删除index.db文件: %s", index_db_path.c_str());
        } else {
            LogE("删除index.db文件失败: %s, errno=%d", index_db_path.c_str(), errno);
            delete_success = false;
        }
    }

    // 删除空目录
    if (delete_success) {
        if (rmdir(dir_path.c_str()) == 0) {
            LogWF("成功删除空目录: %s", dir_path.c_str());

            // 递归检查并删除父目录（如果也为空）
            std::string parent_path = dir_path.substr(0, dir_path.find_last_of('/'));
            if (!parent_path.empty() && parent_path != "/" && parent_path.find("/mnt/custom/disk/disk") == 0) {
                // 只在磁盘路径范围内递归，且不删除磁盘根目录
                size_t disk_root_end = parent_path.find('/', strlen("/mnt/custom/disk/disk"));
                if (disk_root_end != std::string::npos && parent_path.length() > disk_root_end + 1) {
                    LogWF("检查父目录是否也为空: %s", parent_path.c_str());
                    tryDeleteEmptyDirectory(parent_path);
                }
            }

            return true;
        } else {
            LogE("删除空目录失败: %s, errno=%d", dir_path.c_str(), errno);
            return false;
        }
    }

    return false;
}

// 添加私有方法用于清理目录中的文件
bool DiskManager::cleanupDirectoryByFiles(const std::string& dir_path, uint64_t& freed_space, 
                                         uint64_t bytesToFree, const std::string& cleanup_path)
{
    // 收集目录中的所有文件
    std::vector<std::string> files_to_delete;
    
    DIR* dir_handle = opendir(dir_path.c_str());
    if (!dir_handle) {
        LogW("无法打开目录: %s", dir_path.c_str());
        return false;
    }
    
    struct dirent* file_entry;
    
    // 先收集所有要删除的文件
    while ((file_entry = readdir(dir_handle)) != NULL) {
        // 跳过 . 和 ..
        if (strcmp(file_entry->d_name, ".") == 0 || strcmp(file_entry->d_name, "..") == 0) {
            continue;
        }
        
        char file_path[768];
        snprintf(file_path, sizeof(file_path), "%s/%s", dir_path.c_str(), file_entry->d_name);
        
        // 检查是否是录像文件或索引文件
        if (strstr(file_entry->d_name, REC_FILE_EXT) || strstr(file_entry->d_name, REC_IDX_EXT)) {
            files_to_delete.push_back(file_path);
        }
    }
    closedir(dir_handle);
    
    // 按文件名排序，确保有序删除
    std::sort(files_to_delete.begin(), files_to_delete.end());
    
    // 在开始删除前先检查磁盘空间是否已经足够
    if (!isCriticalSpace(cleanup_path)) {
        LogW("磁盘空间已经足够，无需删除文件: %s", dir_path.c_str());
        return true;
    }
    
    // 一个一个地删除文件，每删除一个检查是否达到目标
    bool all_files_deleted = true;
    bool space_freed = false;
    
    LogWF("从目录删除文件: %s, 共%d个文件", dir_path.c_str(), (int)files_to_delete.size());
    
    for (const auto& file_path : files_to_delete) {
        struct stat st;
        if (stat(file_path.c_str(), &st) == 0) {
            uint64_t file_size = st.st_size;
            
            // 删除文件
            // 计算当前处理的文件索引
            auto it = std::find(files_to_delete.begin(), files_to_delete.end(), file_path);
            int file_index = (it - files_to_delete.begin()) + 1;
            
            if (unlink(file_path.c_str()) == 0) {
                freed_space += file_size;
                //LogW("成功: [%d/%d] 大小:%llu, 文件名:%s, 已释放总空间: %llu MB", 
                //     file_index, (int)files_to_delete.size(), (unsigned long long)file_size/1024/1024, 
                //     file_path.c_str(), (unsigned long long)freed_space/1024/1024);
            } else {
                LogE("删除目录文件失败: %s, errno=%d", file_path.c_str(), errno);
                all_files_deleted = false;
            }
            
            // 每删除一个文件就检查是否达到目标或者磁盘空间已足够
            if (freed_space >= bytesToFree) {
                LogW("已达到释放空间目标: %llu MB，停止删除", (unsigned long long)bytesToFree/1024/1024);
                space_freed = true;
                break;
            }
            
            // 检查磁盘空间状态，如果不再处于临界状态，则提前结束
            if (!isCriticalSpace(cleanup_path)) {
                LogW("磁盘空间已经足够，提前结束清盘: 已释放=%llu MB, 目标=%llu MB", 
                     (unsigned long long)freed_space/1024/1024, (unsigned long long)bytesToFree/1024/1024);
                space_freed = true;
                break;
            }
        }
    }
    
    // 如果空间已释放足够或者所有文件都删除了，尝试删除目录
    if (space_freed) {
        // 达到目标，结束删除
        LogWF("已满足清盘条件，停止删除: 已释放=%llu MB", (unsigned long long)freed_space/1024/1024);
    }
    else if (files_to_delete.size() == 0) {
        // 处理没有录像文件的空目录情况
        // index.db文件被视为系统文件，不影响空目录判断
        tryDeleteEmptyDirectory(dir_path);
    }
	else if (all_files_deleted && files_to_delete.size() > 0) {
        // 处理删除录像文件后的空目录情况
        // index.db文件被视为系统文件，不影响空目录判断
        tryDeleteEmptyDirectory(dir_path);
    }
    
    return space_freed;
}



// 请求异步清盘（第二阶段优化：使用原子操作检查状态）
void DiskManager::requestAsyncCleanup(int channel_id, uint64_t bytesToFree)
{
    // 参数验证：防止异常大值（超过10TB认为异常）
    if (bytesToFree > 10ULL * 1024 * 1024 * 1024 * 1024) { // 10TB
        LogE("异常的清理目标大小: %llu MB，限制为4GB", (unsigned long long)(bytesToFree/1024/1024));
        bytesToFree = 4ULL * 1024 * 1024 * 1024; // 限制为4GB
    }

    LogI("请求异步清盘: 通道=%d, 目标=%llu MB", channel_id, (unsigned long long)(bytesToFree/1024/1024));

    // 第二阶段优化：使用原子操作检查清理状态
    if (!m_threadStates.cleanup.load()) {
        LogE("清盘线程未运行，无法处理请求");
        return;
    }

    // 使用原子操作快速检查清理状态
    if (m_isCleaningUp.load()) {
        LogI("已有清盘正在进行，忽略本次请求: 通道=%d", channel_id);
        return;
    }

    {
        ScopedLocker cleanupLock(m_cleanupMutex);

        // 双重检查：在锁内再次检查状态
        if (m_cleanupRequested || m_isCleaningUp) {
            LogI("已有清盘请求或正在清盘，忽略本次请求: 通道=%d", channel_id);
            return;
        }

        // 设置清盘请求参数
        m_cleanupRequested = true;
        m_cleanupChannelId = channel_id;
        m_cleanupBytesToFree = bytesToFree;

        // 原子状态变量已删除，使用现有的 m_isCleaningUp

        // 通知清盘线程
        LogI("发送清盘线程唤醒信号");
        m_cleanupCond.NotifyOne();

        LogI("已请求异步清盘: 通道=%d, 目标=%llu MB",
             channel_id, (unsigned long long)(bytesToFree/1024/1024));
    }
}

// 异步清盘线程函数
void* DiskManager::cleanupThreadFunc(void* arg)
{
    DiskManager* pThis = static_cast<DiskManager*>(arg);
    if (pThis) {
        pThis->cleanupThreadWork();
    }
    return NULL;
}

// 异步清盘线程工作函数
void DiskManager::cleanupThreadWork()
{
    LogI("清盘线程启动");
    
    while (m_threadStates.cleanup.load())
    {
        // 声明在外部作用域，使其在整个循环中可见
        int channel_id = -1;
        uint64_t bytesToFree = 0;
        bool need_cleanup = false;
        
        {
            ScopedLocker cleanupLock(m_cleanupMutex);
            
            // 等待清盘请求或退出信号
            while (!m_cleanupRequested && m_threadStates.cleanup.load()) {
                // 将锁转换为TLock*类型
                TLock* pLock = (TLock*)&m_cleanupMutex;
                
                // 使用条件变量等待，不设置超时
                m_cleanupCond.Wait(pLock);
            }
            
            // 检查是否退出
            if (!m_threadStates.cleanup.load()) {
                return;
            }
            
            // 获取清盘参数
            channel_id = m_cleanupChannelId;
            bytesToFree = m_cleanupBytesToFree;
            
            // 设置清盘状态
            m_cleanupRequested = false;
            m_isCleaningUp = true;
            need_cleanup = true;
        }
        
        if (!need_cleanup) {
            continue;
        }
        
        LogI("开始执行异步清盘: 通道=%d, 目标=%llu MB", channel_id, (unsigned long long)(bytesToFree/1024/1024));

        // 获取当前活动磁盘路径（清理线程只对当前活动磁盘进行清理）
        std::string targetDiskPath = getActiveDiskPath();

        LogI("清理目标磁盘: %s", targetDiskPath.c_str());

        // 使用统一接口检查目标磁盘空间状态
        double usagePercent = getDiskUsagePercent(targetDiskPath, true);

        // 使用更宽松的阈值判断是否需要清盘（85%使用率，即15%可用空间）
        bool needCleanup = (usagePercent >= DISK_STOP_THRESHOLD_PERCENT);

        if (!needCleanup) {
            LogI("目标磁盘空间充足(已用%.2f%%)，无需清理: %s", usagePercent, targetDiskPath.c_str());
            // 清盘完成
            {
                ScopedLocker cleanupLock(m_cleanupMutex);
                m_isCleaningUp = false;
            }
            continue;
        }

        LogI("目标磁盘空间不足(已用%.2f%%)，开始清理: %s", usagePercent, targetDiskPath.c_str());

        // 执行清盘操作（只对当前目标磁盘）
        bool success = false;
        bool needRetry = false;

        LogI("开始清理目标磁盘: %s", targetDiskPath.c_str());

        // 尝试按通道清理目标磁盘
        if (channel_id >= 0) {
            LogI("按通道清理: 通道=%d, 磁盘=%s", channel_id, targetDiskPath.c_str());

            // 在清理前再次确认这是当前活动磁盘
            std::string currentActiveDisk = getActiveDiskPath();
            if (normalizePath(currentActiveDisk) != normalizePath(targetDiskPath)) {
                LogI("磁盘已切换，跳转到新磁盘: %s -> %s", targetDiskPath.c_str(), currentActiveDisk.c_str());
                targetDiskPath = currentActiveDisk;
                needRetry = true;
            } else {
                // 执行清理
                success = cleanupChannelSpace(channel_id, bytesToFree, targetDiskPath);

                // 清理后检查磁盘是否被切换
                currentActiveDisk = getActiveDiskPath();
                if (normalizePath(currentActiveDisk) != normalizePath(targetDiskPath)) {
                    LogI("清理过程中磁盘已切换，跳转到新磁盘: %s -> %s",
                         targetDiskPath.c_str(), currentActiveDisk.c_str());
                    targetDiskPath = currentActiveDisk;
                    needRetry = true;
                } else {
                    // 磁盘未切换，检查清理效果
                    if (!isCriticalSpace(targetDiskPath)) {
                        LogI("按通道清理成功: %s", targetDiskPath.c_str());
                        success = true;
                    }
                }
            }
        }

        // 如果按通道清理不成功且无需重试，尝试整体清理
        if (!success && !needRetry) {
            LogI("按通道清理不成功，尝试整体清理: %s", targetDiskPath.c_str());

            // 在整体清理前再次确认这是当前活动磁盘
            std::string currentActiveDisk = getActiveDiskPath();
            if (normalizePath(currentActiveDisk) != normalizePath(targetDiskPath)) {
                LogI("磁盘已切换，跳转到新磁盘: %s -> %s", targetDiskPath.c_str(), currentActiveDisk.c_str());
                targetDiskPath = currentActiveDisk;
                needRetry = true;
            } else {
                // 执行整体清理
                cleanupSpace(bytesToFree);

                // 清理后检查磁盘是否被切换
                currentActiveDisk = getActiveDiskPath();
                if (normalizePath(currentActiveDisk) != normalizePath(targetDiskPath)) {
                    LogI("整体清理过程中磁盘已切换，跳转到新磁盘: %s -> %s",
                         targetDiskPath.c_str(), currentActiveDisk.c_str());
                    targetDiskPath = currentActiveDisk;
                    needRetry = true;
                } else {
                    // 磁盘未切换，使用统一接口检查清理效果
                    double finalUsagePercent = getDiskUsagePercent(targetDiskPath, true);

                    LogI("清理后磁盘状态: %s 已用=%.2f%%", targetDiskPath.c_str(), finalUsagePercent);

                    // 使用统一阈值判断清理效果（目标：使用率降到90%以下）
                    if (finalUsagePercent < DISK_WARNING_THRESHOLD_PERCENT) {
                        LogI("整体清理成功: %s 已用空间降到%.2f%%", targetDiskPath.c_str(), finalUsagePercent);
                        success = true;
                    } else {
                        LogW("整体清理效果不佳: %s 已用空间仍有%.2f%%", targetDiskPath.c_str(), finalUsagePercent);
                    }
                }
            }
        }

        // 如果需要重试（磁盘已切换），对新磁盘重新进行清理判定
        if (needRetry) {
            LogI("磁盘已切换到: %s，重新进行清理判定", targetDiskPath.c_str());

            // 使用统一接口检查新磁盘是否需要清理
            double newDiskUsage = getDiskUsagePercent(targetDiskPath, true);
            if (newDiskUsage < DISK_WARNING_THRESHOLD_PERCENT) {
                LogI("新磁盘空间充足(已用%.2f%%)，清理完成: %s", newDiskUsage, targetDiskPath.c_str());
                success = true;
            } else {
                LogI("新磁盘需要清理(已用%.2f%%)，重新设置清理请求: %s", newDiskUsage, targetDiskPath.c_str());
                // 重新设置清理请求，针对新磁盘
                {
                    ScopedLocker cleanupLock(m_cleanupMutex);
                    m_cleanupRequested = true;
                    // 保持原有的清理参数
                }
                success = false; // 标记为需要继续清理
            }
        }
        
        // 清盘完成，记录详细状态
        {
            ScopedLocker cleanupLock(m_cleanupMutex);
            m_isCleaningUp = false;
        }

        // 使用统一接口记录清盘结果的详细信息
        double finalUsagePercent = getDiskUsagePercent(targetDiskPath, true);

        if (needRetry) {
            LogI("清盘轮次完成(磁盘已切换): 通道=%d, 目标磁盘=%s, 状态=%s, 最终已用=%.2f%%",
                 channel_id, targetDiskPath.c_str(), success ? "新磁盘无需清理" : "新磁盘需要清理", finalUsagePercent);
        } else {
            LogI("清盘轮次完成: 通道=%d, 目标磁盘=%s, 结果=%s, 最终已用=%.2f%%, 目标释放=%lluMB",
                 channel_id, targetDiskPath.c_str(), success ? "清理成功" : "清理不足",
                 finalUsagePercent, (unsigned long long)(bytesToFree/1024/1024));
        }
    }
    
    LogI("清盘线程退出");
}

// 定期磁盘检查线程入口函数
void* DiskManager::periodicCheckThreadFunc(void* arg)
{
    DiskManager* pThis = static_cast<DiskManager*>(arg);
    if (pThis) {
        pThis->periodicCheckWork();
    }
    return NULL;
}

// 定期磁盘检查线程工作函数
void DiskManager::periodicCheckWork()
{
    LogI("定期磁盘检查线程启动");
    
    // 线程启动后等待一段时间再开始第一次检查，减少初始等待时间
    sleep(15); 
    
    while (m_threadStates.periodicCheck.load()) {

        // 检查是否有后台更新请求
        if (m_backgroundUpdateRequested.exchange(false)) {
            LogI("执行后台磁盘状态更新");
            updateAllDiskStatusInternal();
        }

        // 获取活动磁盘路径
        std::string diskPath = normalizePath(getActiveDiskPath());

		if (!isMounted(diskPath)){
			// 静态变量避免频繁打印相同错误
			static time_t last_mount_error_time = 0;
			static std::string last_error_path;
			time_t current_time = time(nullptr);

			// 只在非静默模式下且满足条件时打印日志
			if (!m_silentMode.load() && (last_error_path != diskPath || (current_time - last_mount_error_time) >= 120)) {
				LogW("磁盘未挂载，跳过检查: %s", diskPath.c_str());
				last_mount_error_time = current_time;
				last_error_path = diskPath;
			}
			Sleep(1000);
			continue;
		}

        // 使用统一接口检查磁盘状态并触发清盘（如果需要）
        double usagePercent = getDiskUsagePercent(diskPath, true);
        LogWF("定期检查磁盘空间: 路径=%s, 已用=%.2f%%, 状态=%s",
            diskPath.c_str(), usagePercent,
            usagePercent >= DISK_CRITICAL_THRESHOLD_PERCENT ? "严重不足" :
            usagePercent >= DISK_CLEANUP_THRESHOLD_PERCENT ?  "空间不足" :
            usagePercent >= DISK_WARNING_THRESHOLD_PERCENT ?  "警告" : "正常");

        // 使用统一清盘触发逻辑
        checkAndTriggerCleanupIfNeeded(diskPath, "periodicCheck");
        
        // 策略功能已删除，直接检查是否需要切换磁盘
        // 使用强制检查获取最新的磁盘状态
        bool disk_full = isDiskFull(diskPath, true);

        if (disk_full) {
            LogW("定期检查: 当前磁盘已满，尝试自动切换");

            // 如果切换成功，不要在同一次检查中重复切换
            if (checkAndSwitchDiskIfNeeded()) {
                LogI("已经完成磁盘切换，不需要进一步操作");

                // 获取新的活动磁盘路径，确保使用最新状态
                diskPath = normalizePath(getActiveDiskPath());
                LogW("切换后的活动磁盘路径: %s", diskPath.c_str());
            }
        }
        
        // 等待指定时间后再次检查
        int sleepTime = m_checkIntervalMinutes * 60;
        
        // 分段睡眠，降低检查间隔以更快响应终止请求
        for (int i = 0; i < sleepTime && m_threadStates.periodicCheck.load(); i += 3) { // 从10秒降为3秒
            sleep(3);
        }
    }
    
    LogI("定期磁盘检查线程退出");
}

// removeEmptyDirectory() 方法已删除

// 实现磁盘分区和挂载相关的静态方法

void DiskManager::getFirstPartition(const char *dev, char *out, size_t out_size)
{
    snprintf(out, out_size, "%s1", dev);
}

int DiskManager::mountDisk(const char *device)
{
    if (!device || !*device) {
        LogE("挂载磁盘失败: 设备参数为空");
        return FAIL;
    }

    LogI("开始阻塞挂载磁盘: %s", device);

    // 使用阻塞方式，确保挂载完成后才返回
    bool success = mountDiskInternal(std::string(device));

    return success ? OK : FAIL;

}

int DiskManager::deleteFormatPartition(const char *dev_disk)
{
    int ret = 0;
    char cmd[256];
    char part[256];
    int retry_count = 0;
    const int max_retries = 2;  // 最大重试次数从3减少到2
    
    // 开始时间记录
    time_t format_start_time = time(NULL);

    // 参数验证
    if (!dev_disk || !*dev_disk) {
        LogE("分区格式化失败: 设备路径为空");
        return FAIL;
    }
    
    LogI("准备格式化磁盘: %s", dev_disk);

    // 关闭所有通道的录像，确保磁盘可以被卸载
    stop_all_record_channels();
    sleep(1);  // 从2秒减少到1秒

    // 检测参数
    FILE *fp = fopen(dev_disk, "r");
    if (!fp) {
        LogE("打开设备失败: %s", dev_disk);
        return FAIL;
    }
    fclose(fp);
    LogI("设备存在并可访问: %s", dev_disk);

    memset(part, 0, sizeof(part));
    snprintf(part, sizeof(part), "%s1", dev_disk);	// /dev/sda1
    LogI("目标分区: %s", part);

    // 强制卸载所有挂载在该磁盘下的分区
    LogI("卸载所有相关分区");
    
    // 同步文件系统缓存
    LogI("同步文件系统缓存");
    sync();
    
    // 卸载文件系统 (不杀死进程，因为只有当前进程在使用)
    LogI("卸载文件系统");
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "mount | grep %s | awk '{print $3}' | xargs -r -n1 umount -f", dev_disk);
    LogI("卸载命令: %s", cmd);
    
    retry_count = 0;
    bool umount_success = false;
    
    while (retry_count < max_retries) {
        ret = system_no_fd(cmd);
        if (ret == 0) {
            LogI("卸载分区成功");
            umount_success = true;
            break;
        } else {
            LogW("卸载分区失败，返回码 %d，正在重试 (%d/%d)", ret, retry_count+1, max_retries);
            sleep(1 + retry_count);  // 递增等待时间，但减少基础值（从2秒减到1秒）
            retry_count++;
        }
    }
    
    if (!umount_success && retry_count >= max_retries) {
        // 尝试使用lazy umount作为最后手段
        LogW("常规卸载失败，尝试使用lazy umount作为最后手段");
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "mount | grep %s | awk '{print $3}' | xargs -r -n1 umount -l", dev_disk);
        ret = system_no_fd(cmd);
        
        if (ret != 0) {
            LogE("所有卸载尝试均失败，无法继续格式化");
            return FAIL;
        } else {
            LogW("使用lazy umount成功卸载");
            umount_success = true;
        }
    }
    
    // 确保同步所有缓冲区到磁盘
    sync();

    LogI("4. 清除磁盘开始部分数据 (%s)...", dev_disk);
    memset(cmd, 0, sizeof(cmd));
    // 增加写入量确保完全擦除旧分区表
    snprintf(cmd, sizeof(cmd), "dd if=/dev/zero of=%s bs=1M count=20", dev_disk);
    ret = system_no_fd(cmd);
    if (ret != 0) {
        LogW("清除磁盘数据可能不完全: ret=%d，继续执行", ret);
    }
    sync();  // 确保写入操作完成
    
    // 额外清除分区开始部分
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "dd if=/dev/zero of=%s bs=512 count=1", dev_disk);
    system_no_fd(cmd);
    sync();

    // 创建 GPT 分区表（会清除原有所有分区）
    LogI("5. 创建GPT分区表");
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "parted -s %s mklabel gpt", dev_disk);
    
    retry_count = 0;
    bool gpt_success = false;
    
    while (retry_count < max_retries) {
        ret = system_no_fd(cmd);
        if (ret == 0) {
            LogI("创建GPT分区表成功");
            gpt_success = true;
            break;
        } else {
            LogW("创建GPT分区表失败，返回码 %d，正在重试 (%d/%d)", ret, retry_count+1, max_retries);
            
            // 尝试先清除更多数据
            if (retry_count == 1) {
                LogW("尝试清除更多磁盘数据");
                memset(cmd, 0, sizeof(cmd));
                snprintf(cmd, sizeof(cmd), "dd if=/dev/zero of=%s bs=1M count=50", dev_disk);
                system_no_fd(cmd);
                sync();
            }
            
            sleep(3);  // 等待3秒后重试
            retry_count++;
        }
    }
    
    if (!gpt_success) {
        // 如果GPT失败，尝试使用MBR分区表
        LogW("创建GPT分区表失败，尝试使用MBR分区表");
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "parted -s %s mklabel msdos", dev_disk);
        ret = system_no_fd(cmd);
        
        if (ret != 0) {
            LogE("创建分区表失败(GPT和MBR都失败)");
            return FAIL;
        } else {
            LogI("成功创建MBR分区表");
            gpt_success = true;  // 使用MBR代替
        }
    }

    // 让内核刷新分区表
    LogI("6. 刷新分区表");
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "partprobe -s %s", dev_disk);
    system_no_fd(cmd);
    sleep(3);  // 给系统足够的时间刷新分区表

    // 创建分区表 (一个硬盘一个) 注意 %%
    LogI("7. 创建主分区");
    memset(cmd, 0, sizeof(cmd));
    snprintf(cmd, sizeof(cmd), "parted --align=opt -s %s unit MiB mkpart primary 1 100%%", dev_disk);
    
    retry_count = 0;
    bool part_success = false;
    
    while (retry_count < max_retries) {
        ret = system_no_fd(cmd);
        if (ret == 0) {
            LogI("创建分区成功");
            part_success = true;
            break;
        } else {
            LogW("创建分区失败，返回码 %d，正在重试 (%d/%d)", ret, retry_count+1, max_retries);
            sleep(3);
            retry_count++;
        }
    }
    
    if (!part_success) {
        // 尝试不指定文件系统类型
        LogW("使用指定参数创建分区失败，尝试简化参数");
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "parted -s %s unit MiB mkpart primary 1 100%%", dev_disk);
        ret = system_no_fd(cmd);
        
        if (ret != 0) {
            LogE("创建分区多次尝试后仍然失败");
            return FAIL;
        } else {
            LogI("使用简化参数创建分区成功");
            part_success = true;
        }
    }

    // 让内核刷新分区表并等待分区设备文件出现
    LogI("8. 刷新分区表并等待设备就绪");
    
    // 多次刷新分区表
    for (int i = 0; i < 3; i++) {
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "partprobe -s %s", dev_disk);
        system_no_fd(cmd);
        sleep(1);
    }
    
    // 等待分区设备文件出现
    int wait_count = 0;
    const int max_wait = 20;  // 增加等待时间到20秒
    bool device_ready = false;
    
    LogI("等待分区设备文件出现: %s", part);
    while (wait_count < max_wait) {
        if (access(part, F_OK) == 0) {
            LogI("分区设备文件已就绪: %s", part);
            sleep(2);  // 再多等两秒，确保设备完全就绪
            device_ready = true;
            break;
        }
        LogI("等待分区设备文件出现: %s (%d/%d)", part, wait_count+1, max_wait);
        sleep(1);
        wait_count++;
        
        // 如果等待超过5秒，再次尝试刷新分区表
        if (wait_count % 5 == 0) {
            LogW("设备未出现，再次刷新分区表");
            memset(cmd, 0, sizeof(cmd));
            snprintf(cmd, sizeof(cmd), "partprobe -s %s", dev_disk);
            system_no_fd(cmd);
            
            // 检查内核消息
            memset(cmd, 0, sizeof(cmd));
            snprintf(cmd, sizeof(cmd), "dmesg | tail -n 10");
            system_no_fd(cmd);
        }
    }
    
    if (!device_ready) {
        LogE("分区设备文件未能在指定时间内出现: %s", part);
        return FAIL;
    }

    // 格式化新分区 - 使用更简单的选项
    LogI("9. 格式化分区: %s", part);
    memset(cmd, 0, sizeof(cmd));
    // 使用更可靠的格式化参数，去掉可能导致问题的高级选项
    snprintf(cmd, sizeof(cmd), "mkfs.ext4 -F -m 0 %.179s 2>&1", part);
    
    retry_count = 0;
    bool format_success = false;
    
    while (retry_count < max_retries) {
        LogI("开始格式化 (尝试 %d/%d)...", retry_count+1, max_retries);
        ret = system_no_fd(cmd);
        if (ret == 0) {
            LogI("格式化成功");
            sync();  // 确保所有操作都已写入磁盘
            format_success = true;
            break;
        } else {
            LogW("格式化失败，返回码 %d，正在重试 (%d/%d)", ret, retry_count+1, max_retries);
            
            // 如果是第一次失败，尝试使用更简单的格式化选项
            if (retry_count == 0) {
                LogW("尝试使用最基本的格式化选项");
                memset(cmd, 0, sizeof(cmd));
                snprintf(cmd, sizeof(cmd), "mkfs.ext4 -F %.179s 2>&1", part);
            }
            
            sleep(5);  // 格式化失败后等待更长时间
            retry_count++;
        }
    }
    
    if (!format_success) {
        LogE("格式化多次尝试后仍然失败");
        return FAIL;
    }

    // 确保所有写入操作都已完成
    sync();
    sleep(2);
    sync();
    
    // 计算耗时
    time_t format_end_time = time(NULL);
    LogI("分区和格式化过程全部完成，耗时: %ld秒", (long)(format_end_time - format_start_time));
    
    return OK;
}

int DiskManager::formatDisk(const char *disk)
{
    int ret = 0;

    if (!disk || !*disk) {
        LogE("格式化失败: 磁盘参数为空");
        return FAIL;
    }

    if (strstr(disk, "/dev/") != disk) {
        LogE("格式化失败: 非法磁盘路径：%s", disk);
        return FAIL;
    }

    // 标记开始格式化，阻止jpg保存操作
    g_disk_formatting.store(true, std::memory_order_release);

    // 记录开始时间
    time_t start_time = time(NULL);
    LogI("=== 开始磁盘格式化: %s ===", disk);

    // 🔥 UUID管理规范：格式化前清理旧UUID映射
    LogI("=== UUID管理：格式化前清理阶段 ===");
    LogI("检查设备旧UUID映射: %s", disk);

    // 先获取当前UUID（如果存在）
    std::string oldUuid = m_uuidManager->getDiskUUID(disk);
    if (!oldUuid.empty()) {
        LogI("发现设备旧UUID: %s --> %s", disk, oldUuid.c_str());
    } else {
        LogI("设备无旧UUID: %s", disk);
    }

    // 清理旧UUID映射
    bool uuid_removed = m_uuidManager->removeUUIDMappingByDevice(disk);
    if (uuid_removed) {
        LogI("✅ 成功清理旧UUID映射: %s", disk);
    } else {
        LogI("ℹ️  未找到旧UUID映射或清理失败: %s", disk);
    }

    LogI("=== UUID清理阶段完成 ===");

    // 重新分区并格式化
    ret = deleteFormatPartition(disk);
    if (ret != OK) {
        LogE("分区格式化失败: %s", disk);
        g_disk_formatting.store(false, std::memory_order_release);
        return ret;
    }
    LogI("分区格式化成功");

    // 挂载（使用内部阻塞版本，确保格式化后立即可用）
    char part_dev[128] = {0};
    snprintf(part_dev, sizeof(part_dev), "%s1", disk);

    // 格式化后需要立即挂载，使用内部实现确保同步完成
    bool mount_success = mountDiskInternal(std::string(part_dev));
    if (!mount_success) {
        LogE("挂载失败: %s", part_dev);
        g_disk_formatting.store(false, std::memory_order_release);
        return FAIL;
    }
    LogI("挂载成功");

    // 创建基本目录结构
    // 从DISK_MAPPING中查找对应的挂载点
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
    char *mountpoint = nullptr;

    for (int i = 0; i < MAX_DISK_COUNT; ++i) {
        if (strcmp(part_dev, disks[i].device) == 0) {
            mountpoint = disks[i].mountpoint;
            break;
        }
    }

    if (mountpoint) {
        LogI("在挂载点创建必要目录结构: %s", mountpoint);
        // 创建存储录像文件的基本目录结构
        
        // 获取当前日期，创建日期目录
        time_t now = time(NULL);
        struct tm *now_tm = localtime(&now);
        char date_dir[32];
        snprintf(date_dir, sizeof(date_dir), "%04d%02d%02d", 
                now_tm->tm_year+1900, now_tm->tm_mon+1, now_tm->tm_mday);
        
        char cmd[256];
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "mkdir -p %s/%s", mountpoint, date_dir);
        system_no_fd(cmd);

        // 创建事件图片目录
        memset(cmd, 0, sizeof(cmd));
        snprintf(cmd, sizeof(cmd), "mkdir -p %s/event_pic", mountpoint);
        system_no_fd(cmd);
        
        LogI("目录结构创建完成");
        
        // 执行sync以确保所有更改都写入磁盘
        sync();

        // 格式化成功后，更新磁盘管理器状态
        LogI("格式化成功，更新磁盘管理器状态...");

        // 将新挂载的磁盘添加到磁盘路径列表中
        std::string mountPointStr = std::string(mountpoint);
        auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), mountPointStr);
        if (it == m_diskPaths.end()) {
            // 🔥 USB路径分离：只添加非USB设备路径
            if (!isUsbDevicePath(mountPointStr)) {
                m_diskPaths.push_back(mountPointStr);
                LogI("添加新磁盘到路径列表: %s", mountPointStr.c_str());
            } else {
                LogI("跳过USB设备路径: %s", mountPointStr.c_str());
            }
        }

        // 🔥 USB路径分离：只有非USB设备才能设置为活动磁盘
        if (!isUsbDevicePath(mountPointStr)) {
            m_activeDiskPath = mountPointStr;
            LogI("设置活动磁盘路径: %s", m_activeDiskPath.c_str());
        } else {
            LogI("USB设备不能设置为活动磁盘: %s", mountPointStr.c_str());
        }

        // 触发状态更新
        m_backgroundUpdateRequested = true;

        // 🔥 UUID管理规范：格式化后重建UUID映射
        LogI("=== UUID管理：格式化后重建阶段 ===");
        LogI("获取格式化后的新UUID: %s", part_dev);

        std::string newUuid = m_uuidManager->getDiskUUID(part_dev);
        if (!newUuid.empty()) {
            LogI("✅ 成功获取新UUID: %s --> %s", part_dev, newUuid.c_str());

            // 清理可能的重复UUID（格式化可能产生相同UUID）
            m_uuidManager->cleanupDuplicateUUIDs(newUuid);

            // 添加新UUID映射
            m_uuidManager->addUUIDMapping(newUuid, mountPointStr);
            LogI("✅ 成功添加新UUID映射: %s --> %s", newUuid.c_str(), mountPointStr.c_str());

            // 验证UUID一致性
            bool consistent = m_uuidManager->validateUUIDConsistency(part_dev);
            if (consistent) {
                LogI("✅ UUID一致性验证通过: %s", part_dev);
            } else {
                LogE("❌ UUID一致性验证失败: %s", part_dev);
                // 尝试自动修复
                LogI("尝试自动修复UUID一致性问题...");
                m_uuidManager->cleanupDuplicateUUIDs(newUuid);
            }

            LogI("=== UUID重建阶段完成 ===");
        } else {
            LogE("❌ 格式化后无法获取新UUID: %s", part_dev);
            LogE("⚠️  UUID重建失败，但不影响格式化流程");
            // 注意：UUID操作失败不影响格式化流程，继续执行
        }

        // 验证磁盘状态
        bool hasAvailable = hasAvailableDisk();
        LogI("格式化后磁盘状态检查: %s", hasAvailable ? "有可用磁盘" : "无可用磁盘");
    }

    // 格式化完成后，重新启动所有录像通道
    LogI("准备重新启动所有录像通道...");
    start_all_record_channels();

    // 计算总耗时
    time_t end_time = time(NULL);
    LogI("=== 磁盘格式化完成 ===");
    LogI("总耗时: %ld秒", (long)(end_time - start_time));


	Sleep(1000);

    // 恢复标志，允许保存jpg图片
    g_disk_formatting.store(false, std::memory_order_release);

	sync();

	Sleep(2000);

    return OK;
}

// 检查磁盘是否满了需要切换
bool DiskManager::isDiskFull(const std::string& path, bool force_check)
{
    if (path.empty()) {
        if (force_check) {
            LogW("路径为空，视为已满");
        }
        return true;
    }

    // 使用统一接口获取磁盘使用率
    double usagePercent = getDiskUsagePercent(path, force_check);

    // 注意：不在这里调用 checkAndTriggerCleanupIfNeeded，避免递归调用
    // checkAndTriggerCleanupIfNeeded 会调用 checkAndSwitchDiskIfNeeded
    // 而 checkAndSwitchDiskIfNeeded 又会调用 isDiskFull，形成递归

    // 使用90%阈值判断磁盘是否需要切换（符合用户需求）
    bool result = usagePercent >= DISK_SWITCH_THRESHOLD_PERCENT;

    // 只在强制检查或状态变化时输出详细日志
    static std::map<std::string, bool> last_reported_state;
    std::string normPath = normalizePath(path);
    bool state_changed = last_reported_state.find(normPath) == last_reported_state.end() ||
                         last_reported_state[normPath] != result;

    if (force_check || state_changed) {
        if (force_check) {
            LogW("磁盘空间: %s, 已用=%.2f%%, 状态=%s",
                normPath.c_str(), usagePercent, result ? "已满" : "正常");
        } else {
            LogI("磁盘空间: %s, 已用=%.2f%%, 状态=%s",
                normPath.c_str(), usagePercent, result ? "已满" : "正常");
        }
        last_reported_state[normPath] = result;
    }

    return result;
}

// 自动切换模式 - 检查是否需要切换磁盘
bool DiskManager::checkAndSwitchDiskIfNeeded()
{
    // 使用ScopedLocker确保线程安全
    ScopedLocker lock(g_cleanupLock);
    bool result = false;

    LogI("开始检查磁盘切换需求...");
    
    // 策略功能已删除，直接执行切换逻辑
    {
        // 获取当前活动磁盘路径，并确保路径格式一致
        std::string currentPath = normalizePath(getActiveDiskPath());
        
        // 检查是否已经在缓存中标记为已切换
        static std::string lastSwitchedFrom;
        static std::string lastSwitchedTo;
        static time_t lastSwitchTime = 0;
        static int consecutiveSwitchAttempts = 0;
        time_t now = time(NULL);
        
        // 防止在短时间内频繁切换
        const int DEBOUNCE_TIME = 30; // 30秒内不重复切换
        
        // 如果最近已经进行了切换，避免频繁切换
        if (now - lastSwitchTime < DEBOUNCE_TIME) {
            std::string normCurrentPath = normalizePath(currentPath);
            
            // 如果进行了多次重复切换尝试，可能是路径问题，打印详细日志并增加延迟
            if (consecutiveSwitchAttempts > 3) {
                LogW("频繁切换尝试 (%d次)，可能存在路径问题", consecutiveSwitchAttempts);
                // 对于频繁切换，强制延长等待时间
                return true;
            }
            
            // 正常的切换防抖逻辑
            if (!lastSwitchedFrom.empty() && !lastSwitchedTo.empty()) {
                std::string normLastSwitchedTo = normalizePath(lastSwitchedTo);
                
                // 如果当前磁盘是上次切换的目标，跳过这次切换
                if (normCurrentPath == normLastSwitchedTo) {
                    return true;
                }
            }
            
            consecutiveSwitchAttempts++; // 增加连续切换尝试计数
        } 
		else {
            consecutiveSwitchAttempts = 0; // 重置计数器
        }
        
        // 检查磁盘路径列表大小
        if (m_diskPaths.size() <= 1) {
            // 如果只有一个磁盘，直接触发清理，避免递归调用
            LogI("只有一个磁盘，无法切换，触发清理操作");
            uint64_t bytesToFree = 1024 * 1024 * 1024; // 释放1GB空间
            requestAsyncCleanup(0, bytesToFree);

            return false;
        }
        
        // 使用统一接口检查当前磁盘是否已满（强制检查，不使用缓存）
        bool disk_full = isDiskFull(currentPath, true);

        if (disk_full) {
            LogW("磁盘已满，需要切换: %s", currentPath.c_str());

            // 检查是否已经有待处理的磁盘切换
            if (isDiskSwitchPending()) {
                LogI("磁盘切换已在待处理状态，跳过重复设置");
                return false;
            }

            // 查找下一个可用磁盘
            std::string nextDisk = findNextAvailableDisk(currentPath);
            if (nextDisk.empty()) {
                LogW("没有找到可用的切换目标磁盘");
                return false;
            }

            // 检查是否已有延迟切换在等待，如果有且未超时，则跳过重复设置
            time_t now = time(NULL);
            bool hasPendingSwitch = false;
            bool pendingSwitchTimeout = false;

            {
                ScopedLocker pendingLock(m_pendingSwitchLock);
                hasPendingSwitch = m_diskSwitchPending.load();
                if (hasPendingSwitch && m_pendingSwitchSetTime > 0) {
                    pendingSwitchTimeout = (now - m_pendingSwitchSetTime) >= PENDING_SWITCH_TIMEOUT_SECONDS;
                }
            }

            // 如果有延迟切换且未超时，跳过重复设置
            if (hasPendingSwitch && !pendingSwitchTimeout) {
                LogI("延迟切换已设置且未超时，跳过重复设置");
                return true;
            }

            // 如果延迟切换超时，清除旧的延迟切换状态
            if (hasPendingSwitch && pendingSwitchTimeout) {
                LogW("延迟切换已超时(%d秒)，清除旧状态并继续处理", PENDING_SWITCH_TIMEOUT_SECONDS);
                clearPendingSwitch();
            }

            // 设置新的延迟切换：不立即切换，而是等待下次文件切换时机
            LogI("启动平滑过渡机制：设置延迟磁盘切换到 %s", nextDisk.c_str());
            setPendingSwitchTarget(nextDisk);

            // 记录延迟切换设置时间
            {
                ScopedLocker pendingLock(m_pendingSwitchLock);
                m_pendingSwitchSetTime = now;
            }

            // 通知录像模块有磁盘切换待处理（但不强制立即切换）
            notifyDiskSwitchPending();

            LogI("磁盘切换已设置为延迟模式，将在下次录像文件切换时执行，超时时间: %d秒", PENDING_SWITCH_TIMEOUT_SECONDS);

            // 延迟切换已设置，现在检查是否需要立即执行回退机制
            // 等待一小段时间让延迟切换有机会被录像模块执行
            LogI("延迟切换已设置，等待录像模块执行或超时回退");

            // 检查延迟切换是否在短时间内被执行
            bool delayedSwitchExecuted = false;
            for (int i = 0; i < 3; i++) { // 等待最多3秒
                sleep(1);
                if (!m_diskSwitchPending.load()) {
                    delayedSwitchExecuted = true;
                    LogI("延迟切换已被录像模块执行");
                    break;
                }
            }

            // 如果延迟切换已执行，返回成功
            if (delayedSwitchExecuted) {
                return true;
            }

            // 延迟切换未在短时间内执行，启动回退机制
            LogW("延迟切换未在预期时间内执行，启动直接切换回退机制");

            // 清除延迟切换状态，避免冲突
            clearPendingSwitch();

            {
                // 直接切换回退逻辑
                LogI("执行直接切换回退：寻找下一个可用磁盘");

                std::string fallbackDisk = findNextAvailableDisk(currentPath);
                if (!fallbackDisk.empty()) {
                    LogI("找到可用磁盘，执行直接切换回退: %s", fallbackDisk.c_str());
                    // 直接切换到下一个磁盘
                    result = switchToNextDisk();
                } else {
                    // 没有找到可用磁盘，检查2小时等待机制
                    static time_t lastAllDiskFullTime = 0;
                    time_t now = time(NULL);

                    if (lastAllDiskFullTime == 0) {
                        // 第一次发现所有磁盘都满
                        lastAllDiskFullTime = now;
                        LogW("所有磁盘已满，开始2小时等待计时");

                        // 在当前磁盘执行清理，尝试腾出空间
                        LogI("在当前磁盘执行清理，尝试腾出空间");
                        uint64_t bytesToFree = 1024 * 1024 * 1024; // 释放1GB空间
                        requestAsyncCleanup(0, bytesToFree);

                    } else if (now - lastAllDiskFullTime >= 2 * 3600) { // 2小时间隔
                        LogW("所有磁盘已满且达到2小时等待时间，强制切换到下一个磁盘");

                        // 重置计时器
                        lastAllDiskFullTime = now;
                        m_lastCleanupTime = now;

                        // 强制切换到下一个磁盘（即使空间不足）
                        size_t nextIndex = (m_activeDiskIndex + 1) % m_diskPaths.size();
                        std::string nextDisk = m_diskPaths[nextIndex];

                        LogI("强制切换到磁盘: %s (索引:%zu)，将边写入边清理", nextDisk.c_str(), nextIndex);

                        // 更新活动磁盘
                        m_activeDiskIndex = nextIndex;
                        m_activeDiskPath = normalizePath(nextDisk);

                        // 立即对新磁盘执行大容量清理
                        uint64_t bytesToFree = 2048 * 1024 * 1024; // 释放2GB空间
                        requestAsyncCleanup(0, bytesToFree);

                        result = true;

                    } else {
                        // 还在2小时等待期内
                        time_t remaining = 2 * 3600 - (now - lastAllDiskFullTime);
                        LogI("所有磁盘已满，等待中... 剩余时间: %ld 分钟", remaining / 60);

                        // 继续在当前磁盘执行清理
                        uint64_t bytesToFree = 512 * 1024 * 1024; // 释放512MB空间
                        requestAsyncCleanup(0, bytesToFree);
                    }
                }
            }
        } else {
            // 磁盘未满，重置2小时等待计时器
            static time_t lastAllDiskFullTime = 0;
            if (lastAllDiskFullTime != 0) {
                LogI("磁盘空间恢复正常，重置2小时等待计时器");
                lastAllDiskFullTime = 0;
            }

            LogI("磁盘未满(使用率<90%%)，不需要切换");
        }
    } // 策略功能已删除，直接执行切换逻辑

    // 记录切换检查结果
    if (result) {
        std::string newActiveDisk = getActiveDiskPath();
        LogI("磁盘切换检查完成: 已切换到 %s", newActiveDisk.c_str());

        // 更新切换记录
        static std::string lastSwitchedFrom;
        static std::string lastSwitchedTo;
        static time_t lastSwitchTime = 0;

        lastSwitchedFrom = lastSwitchedTo;
        lastSwitchedTo = newActiveDisk;
        lastSwitchTime = time(NULL);

        LogI("磁盘切换记录: %s -> %s (时间: %ld)",
             lastSwitchedFrom.c_str(), lastSwitchedTo.c_str(), lastSwitchTime);

        // 重置录像通道的磁盘切换状态，允许新磁盘上的录像继续
        reset_all_channels_disk_switch_state();

    } else {
        LogI("磁盘切换检查完成: 无需切换");
    }

    return result;
}

// 主动触发磁盘切换检查（供录像模块调用）
void DiskManager::triggerDiskSwitchCheck()
{
    std::string currentPath = getActiveDiskPath();
    if (currentPath.empty()) {
        LogW("当前没有活动磁盘，无法检查切换");
        return;
    }

    // 强制检查当前磁盘使用率
    double usagePercent = getDiskUsagePercent(currentPath, true);

    LogI("主动检查磁盘切换: 当前磁盘=%s, 使用率=%.2f%%", currentPath.c_str(), usagePercent);

    // 如果达到90%阈值，触发切换
    if (usagePercent >= DISK_SWITCH_THRESHOLD_PERCENT) {
        LogW("磁盘使用率达到切换阈值(%.2f%% >= %.1f%%)，触发磁盘切换",
             usagePercent, DISK_SWITCH_THRESHOLD_PERCENT);

        bool switched = checkAndSwitchDiskIfNeeded();
        if (switched) {
            LogI("磁盘切换成功，新磁盘: %s", getActiveDiskPath().c_str());
        } else {
            LogW("磁盘切换失败或无需切换");
        }
    } else {
        LogI("磁盘使用率正常(%.2f%% < %.1f%%)，无需切换",
             usagePercent, DISK_SWITCH_THRESHOLD_PERCENT);
    }
}


// 检查是否有录像正在进行
bool DiskManager::isRecordingInProgress() const
{
    // 调用录像模块的接口检查是否有录像正在进行
    return is_any_recording_in_progress();
}

// 等待录像完成
bool DiskManager::waitForRecordingComplete(int timeoutSeconds)
{
    LogI("等待录像完成，超时时间: %d秒", timeoutSeconds);

    // 调用录像模块的接口等待所有录像完成
    return wait_for_all_recordings_complete(timeoutSeconds);
}

// 通知磁盘切换待处理
void DiskManager::notifyDiskSwitchPending()
{
    LogI("通知所有录像通道准备磁盘切换");

    // 调用录像模块的接口准备磁盘切换
    prepare_all_channels_for_disk_switch();
}

// 检查是否有磁盘切换待处理
bool DiskManager::isDiskSwitchPending() const
{
    return m_diskSwitchPending.load();
}

// 设置待切换的目标磁盘
void DiskManager::setPendingSwitchTarget(const std::string& targetDisk)
{
    ScopedLocker lock(m_pendingSwitchLock);

    LogI("设置待切换目标磁盘: %s", targetDisk.c_str());

    m_pendingSwitchTarget = targetDisk;
    m_diskSwitchPending.store(true);

    LogI("磁盘切换已设置为待处理状态，将在下次文件切换时执行");
}

// 获取待切换的目标磁盘
std::string DiskManager::getPendingSwitchTarget() const
{
    ScopedLocker lock(m_pendingSwitchLock);
    return m_pendingSwitchTarget;
}

// 执行待处理的磁盘切换
bool DiskManager::executePendingDiskSwitch()
{
    ScopedLocker lock(m_pendingSwitchLock);

    if (!m_diskSwitchPending.load()) {
        return false; // 没有待处理的切换
    }

    if (m_pendingSwitchTarget.empty()) {
        LogW("待切换目标磁盘为空，清除待处理状态");
        clearPendingSwitch();
        return false;
    }

    LogI("执行延迟的磁盘切换: %s", m_pendingSwitchTarget.c_str());

    // 执行实际的磁盘切换
    bool result = switchToDisk(m_pendingSwitchTarget);

    if (result) {
        LogI("延迟磁盘切换成功: %s", m_pendingSwitchTarget.c_str());
        clearPendingSwitch();

        // 重置录像通道的磁盘切换状态
        reset_all_channels_disk_switch_state();
    } else {
        LogW("延迟磁盘切换失败: %s", m_pendingSwitchTarget.c_str());
    }

    return result;
}

// 清除待处理的磁盘切换
void DiskManager::clearPendingSwitch()
{
    // 注意：这个函数假设调用者已经持有锁
    LogI("清除磁盘切换待处理状态");

    m_diskSwitchPending.store(false);
    m_pendingSwitchTarget.clear();
    m_pendingSwitchSetTime = 0; // 清除延迟切换设置时间
}


// 优化：检查是否有可用的已挂载磁盘（带缓存机制）
bool DiskManager::hasAvailableDisk(bool forceCheck) const
{
    // 缓存优化：如果不强制检查且缓存有效，直接返回缓存结果
    if (!forceCheck) {
        ScopedLocker lock(m_diskStatusCacheLock);
        if (m_diskStatusCache.isValid()) {
            return m_diskStatusCache.hasAvailableDisk;
        }
    }

    // 执行实际的磁盘检查
    bool hasAvailable = false;

    // 首先检查活动磁盘路径
    if (!m_activeDiskPath.empty() && isMounted(m_activeDiskPath)) {
        hasAvailable = true;
    }

    // 如果活动磁盘不可用，检查磁盘路径列表中的磁盘
    if (!hasAvailable) {
        for (const auto& path : m_diskPaths) {
            if (isMounted(path)) {
                hasAvailable = true;
                break;
            }
        }
    }

    // 如果列表为空或都未挂载，检查DISK_MAPPING中定义的磁盘（实时检查）
    if (!hasAvailable && m_diskPaths.empty()) {
        MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;
        for (int i = 0; i < MAX_DISK_COUNT; i++) {
            if (disks[i].mountpoint && *disks[i].mountpoint) {
                if (isMounted(std::string(disks[i].mountpoint))) {
                    // 发现有挂载的磁盘但不在列表中，记录日志
                    LogI("发现已挂载但未在列表中的磁盘: %s", disks[i].mountpoint);
                    hasAvailable = true;
                    break;
                }
            }
        }
    }

    // 更新缓存
    {
        ScopedLocker lock(m_diskStatusCacheLock);
        m_diskStatusCache.hasAvailableDisk = hasAvailable;
        m_diskStatusCache.lastCheckTime = time(nullptr);
    }

    // 减少日志输出频率
    if (!hasAvailable) {
        static time_t last_log_time = 0;
        time_t now = time(nullptr);
        if (now - last_log_time > 300) { // 每5分钟最多输出一次
            LogW("没有找到任何已挂载的磁盘");
            last_log_time = now;
        }
    }

    return hasAvailable;
}

// 优化：清除硬盘状态缓存（用于硬盘重新接入后的状态刷新）
void DiskManager::clearDiskStatusCache() const
{
    ScopedLocker lock(m_diskStatusCacheLock);
    m_diskStatusCache.lastCheckTime = 0; // 使缓存失效
    LogI("硬盘状态缓存已清除，下次检查将重新扫描");
}

// ============================================================================
// 磁盘空间不足紧急处理实现
// ============================================================================

// 磁盘写入错误紧急处理（非阻塞，多通道并发优化）
bool DiskManager::handleDiskSpaceEmergency(const std::string& failedPath, int channelId)
{
	uint64_t requiredSpace = 1024*1024*1024;
	
    // 从失败路径提取磁盘路径
    std::string diskPath = extractDiskPathFromFile(failedPath);
    if (diskPath.empty()) {
        LogE("无法从失败路径提取磁盘路径: %s", failedPath.c_str());
        return false;
    }

    LogI("提取的磁盘路径: %s", diskPath.c_str());

    // 防重复处理机制：检查是否在短时间内已经处理过相同磁盘
    static std::map<std::string, time_t> lastProcessedTime;
    static TCSLock processedTimeLock;

    time_t now = time(NULL);
    bool skipDuplicateProcessing = false;

    {
        ScopedLocker lock(processedTimeLock);
        auto it = lastProcessedTime.find(diskPath);
        if (it != lastProcessedTime.end() && (now - it->second) < 10) { // 10秒内不重复处理
            skipDuplicateProcessing = true;
            LogI("跳过重复处理，该磁盘在10秒内已处理过: %s", diskPath.c_str());
        } else {
            lastProcessedTime[diskPath] = now;
        }
    }

    // 步骤1: 快速检查当前磁盘状态
    double usagePercent = getDiskUsagePercent(diskPath, true);
    LogE("当前磁盘使用率: %.2f%%", usagePercent);

    // 步骤2: 立即检查是否有足够空间（可能其他通道已经清理过）
    if (hasEnoughSpaceForFile(diskPath)) {
        LogI("磁盘空间已足够，无需处理");
        return true;
    }

    // 步骤3: 非阻塞异步清理（不等待完成）
    if (!skipDuplicateProcessing) {
        uint64_t cleanupSize = std::max(requiredSpace * 2, (uint64_t)(1024 * 1024 * 1024)); // 至少1GB
        requestAsyncCleanup(channelId, cleanupSize);
    }

    // 步骤4: 尝试紧急磁盘切换（如果有可用磁盘）
    std::string nextDisk = findNextAvailableDisk(diskPath);
    if (!nextDisk.empty()) {
        bool switchSuccess = emergencyDiskSwitch(diskPath, channelId);
        if (switchSuccess) {
            LogI("紧急磁盘切换成功");
            return true;
        } else {
            LogW("紧急磁盘切换失败，但异步清理正在进行");
        }
    } else {
        LogI("没有可用的切换目标磁盘");
    }

    // 步骤5: 触发更大规模的异步清理（不等待）
    if (!skipDuplicateProcessing) {
        LogE("触发大规模异步清理");
        // 修复：限制最大清理目标，防止异常大值
        uint64_t largeCleanupSize = std::min(requiredSpace * 4, (uint64_t)(4 * 1024 * 1024 * 1024)); // 最大4GB
        LogI("大规模清理目标: %llu MB", (unsigned long long)(largeCleanupSize/1024/1024));
        requestAsyncCleanup(-1, largeCleanupSize); // 无通道优先级
        LogI("大规模异步清理已触发");
    }

    // 快速返回，让调用方可以立即重试
    LogI("紧急处理完成，建议调用方稍后重试文件创建");
    return false; // 返回false表示需要重试，但处理已启动
}

// 从文件路径提取磁盘路径的辅助函数
std::string DiskManager::extractDiskPathFromFile(const std::string& filePath)
{
    // 文件路径格式: /mnt/custom/disk/disk2/20250717/1/175638_1.dat
    // 需要提取: /mnt/custom/disk/disk2

    if (filePath.empty()) {
        return "";
    }

    // 查找 /mnt/custom/disk/ 部分
    size_t basePos = filePath.find("/mnt/custom/disk/");
    if (basePos == std::string::npos) {
        LogW("文件路径不包含标准磁盘基础路径: %s", filePath.c_str());
        return "";
    }

    // 从基础路径开始查找下一个磁盘目录
    size_t diskStart = basePos + strlen("/mnt/custom/disk/");
    size_t nextSlash = filePath.find('/', diskStart);

    if (nextSlash == std::string::npos) {
        // 如果没有找到下一个斜杠，整个路径就是磁盘路径
        return filePath;
    }

    // 提取磁盘路径
    std::string diskPath = filePath.substr(0, nextSlash);
    LogI("从文件路径 %s 提取磁盘路径: %s", filePath.c_str(), diskPath.c_str());

    return diskPath;
}

// 紧急磁盘切换（快速非阻塞）
bool DiskManager::emergencyDiskSwitch(const std::string& currentPath, int channelId)
{
    LogE("开始紧急磁盘切换");
    LogE("当前磁盘: %s", currentPath.c_str());
    LogE("通道ID: %d", channelId);

    // 快速检查是否有其他可用磁盘
    std::string nextDisk = findNextAvailableDisk(currentPath);
    if (nextDisk.empty()) {
        LogI("没有找到可用的切换目标磁盘");
        return false;
    }

    LogI("找到可用磁盘: %s", nextDisk.c_str());

    // 快速检查目标磁盘是否有足够空间
    if (!hasEnoughSpaceForFile(nextDisk)) { // 至少100MB
        LogW("目标磁盘空间不足: %s", nextDisk.c_str());
        return false;
    }

    // 执行快速磁盘切换
    bool switchResult = switchToNextDisk();
    if (switchResult) {
        LogI("紧急磁盘切换成功: %s -> %s", currentPath.c_str(), getActiveDiskPath().c_str());
        return true;
    } else {
        LogW("紧急磁盘切换失败，但不影响异步清理");
        return false;
    }
}

// 检查磁盘是否有足够空间创建文件
bool DiskManager::hasEnoughSpaceForFile(const std::string& diskPath)
{
	uint64_t requiredSpace = 100 * 1024 * 1024;
		
    if (diskPath.empty()) {
        LogW("磁盘路径为空，无法检查空间");
        return false;
    }

    // 获取磁盘使用率
    double usagePercent = getDiskUsagePercent(diskPath, true);

    // 获取磁盘总空间和可用空间
    struct statvfs vfs;
    if (statvfs(diskPath.c_str(), &vfs) != 0) {
        LogE("无法获取磁盘空间信息: %s, errno=%d (%s)",
             diskPath.c_str(), errno, strerror(errno));
        return false;
    }

    uint64_t availableSpace = (uint64_t)vfs.f_bavail * vfs.f_frsize;

    LogI("磁盘空间检查: %s", diskPath.c_str());
    LogI("- 使用率: %.2f%%", usagePercent);
    LogI("- 可用空间: %llu MB", (unsigned long long)availableSpace / 1024 / 1024);
    LogI("- 需要空间: %llu MB", (unsigned long long)requiredSpace / 1024 / 1024);

    bool hasEnough = availableSpace >= requiredSpace;
    LogI("空间检查结果: %s", hasEnough ? "足够" : "不足");

    return hasEnough;
}

// 移除了emergencySpaceCleanup函数，现在使用现有的requestAsyncCleanup接口

// 移除了emergencyCleanupDateDirectory和emergencyCleanupChannelDirectory函数，现在使用现有的异步清理接口

// processBackgroundTasks() 方法已删除

// 内部挂载磁盘实现（在后台线程中执行）
bool DiskManager::mountDiskInternal(const std::string& device)
{
    LogI("开始后台挂载磁盘: %s", device.c_str());

    try {
        // 智能处理设备路径：确保使用分区而不是整个磁盘
        std::string actualDevice = convertToPartitionPath(device);

        // 检查转换结果
        if (actualDevice.empty()) {
            LogE("设备路径转换失败，设备不存在: %s", device.c_str());
            return false;
        }

        if (actualDevice != device) {
            LogI("设备路径已转换: %s -> %s", device.c_str(), actualDevice.c_str());
        } else {
            LogI("使用原始设备路径: %s", actualDevice.c_str());
        }

        // 获取UUID
        std::string uuid = m_uuidManager->getDiskUUID(actualDevice);
        std::string mountPoint;

        if (!uuid.empty()) {
            mountPoint = m_uuidManager->getMountPointByUUID(uuid);
            if (mountPoint.empty()) {
                mountPoint = m_uuidManager->getNextAvailableMountPoint();
                // 添加UUID映射
                m_uuidManager->addUUIDMapping(uuid, mountPoint);
                // saveUUIDMappings已经在addUUIDMapping中调用，无需重复调用
            }
        } else {
            mountPoint = m_uuidManager->getNextAvailableMountPoint();
        }

        // 创建挂载点目录
        char cmd[512];
        snprintf(cmd, sizeof(cmd), "mkdir -p %s", mountPoint.c_str());
        int result = system_no_fd(cmd);

        if (result != 0) {
            LogE("创建挂载点失败: %s", mountPoint.c_str());
            return false;
        }

        // 执行挂载（使用实际的设备路径）
        snprintf(cmd, sizeof(cmd), "mount -o rw,noatime,nodiratime %s %s", actualDevice.c_str(), mountPoint.c_str());
        result = system_no_fd(cmd);

        if (result == 0) {
            LogI("后台挂载成功: %s -> %s", actualDevice.c_str(), mountPoint.c_str());

            // 确保新挂载的磁盘在管理列表中
            auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), mountPoint);
            if (it == m_diskPaths.end()) {
                // USB路径分离：只添加非USB设备路径
                if (!isUsbDevicePath(mountPoint)) {
                    m_diskPaths.push_back(mountPoint);
                    LogI("将新挂载的磁盘添加到管理列表: %s", mountPoint.c_str());
                } else {
                    LogI("跳过USB设备路径: %s", mountPoint.c_str());
                }
            }

            // 触发状态更新
            m_backgroundUpdateRequested = true;

            // 清除硬盘状态缓存，确保录像模块能立即感知到新挂载的磁盘
            clearDiskStatusCache();

            return true;
        } 
		else {
            LogE("后台挂载失败: %s -> %s, 返回码: %d", actualDevice.c_str(), mountPoint.c_str(), result);
            return false;
        }

    } catch (const std::exception& e) {
        LogE("挂载操作异常: %s", e.what());
        return false;
    }
}

// 内部卸载磁盘实现（在后台线程中执行）
bool DiskManager::unmountDiskInternal(const std::string& path)
{
    LogI("开始后台卸载磁盘: %s", path.c_str());

    try {
        char cmd[512];
        snprintf(cmd, sizeof(cmd), "umount %s", path.c_str());
        int result = system_no_fd(cmd);

        if (result == 0) {
            LogI("后台卸载成功: %s", path.c_str());

            // 触发状态更新
            m_backgroundUpdateRequested = true;

            return true;
        } else {
            LogE("后台卸载失败: %s, 返回码: %d", path.c_str(), result);
            return false;
        }

    } catch (const std::exception& e) {
        LogE("卸载操作异常: %s", e.what());
        return false;
    }
}

// 内部更新磁盘状态实现（第二阶段优化：减少锁持有时间）
void DiskManager::updateAllDiskStatusInternal()
{
    // 第二阶段优化：先复制路径列表，减少锁持有时间
    std::vector<std::string> pathsCopy;
    std::map<std::string, DiskInfo> currentInfoMap;

    // 快速复制数据（短时间持有锁）
    if (!m_diskInfoLock.TryLock()) {
        LogW("更新磁盘状态时锁被占用，跳过本次更新");
        return;
    }

    for (const auto& pair : m_diskInfoMap) {
        pathsCopy.push_back(pair.first);
        currentInfoMap[pair.first] = pair.second;
    }
    m_diskInfoLock.Leave();

    // 在锁外进行耗时的I/O操作
    std::map<std::string, DiskInfo> updatedInfoMap;
    for (const auto& path : pathsCopy) {
        DiskInfo info = currentInfoMap[path];

        // 更新最后检查时间
        info.lastChecked = time(nullptr);

        // 检查挂载状态
        info.isMounted = isMounted(path);

        if (info.isMounted) {
            // 获取磁盘空间信息（耗时操作在锁外）
            struct statvfs stat;
            if (statvfs(path.c_str(), &stat) == 0) {
                info.totalSpace = (uint64_t)stat.f_blocks * stat.f_frsize;
                info.availSpace = (uint64_t)stat.f_bavail * stat.f_frsize;

                // 计算使用率
                double usagePercent = 0.0;
                if (info.totalSpace > 0) {
                    usagePercent = (double)(info.totalSpace - info.availSpace) / info.totalSpace * 100.0;
                }

                // 更新状态（按阈值从高到低判断）
                if (usagePercent >= DISK_CRITICAL_THRESHOLD_PERCENT) {
                    info.status = 1; // 空间不足
                    info.statusMessage = "磁盘空间严重不足";
                } else if (usagePercent >= DISK_CLEANUP_THRESHOLD_PERCENT) {
                    info.status = 1; // 空间不足
                    info.statusMessage = "磁盘空间不足，需要清理";
                } else if (usagePercent >= DISK_SWITCH_THRESHOLD_PERCENT) {
                    info.status = 1; // 空间不足
                    info.statusMessage = "磁盘空间不足，需要切换";
                } else {
                    info.status = 0; // 正常
                    info.statusMessage = "正常";
                }
            } else {
                info.status = 3; // 错误
                info.statusMessage = "无法获取磁盘空间信息";
            }
        } else {
            info.status = 2; // 不可用
            info.statusMessage = "未挂载";
            info.totalSpace = 0;
            info.availSpace = 0;
        }

        updatedInfoMap[path] = info;
    }

    // 批量更新（短时间持有锁）
    if (m_diskInfoLock.TryLock()) {
        for (const auto& pair : updatedInfoMap) {
            m_diskInfoMap[pair.first] = pair.second;
        }
        m_diskInfoLock.Leave();

        // 第二阶段优化：更新挂载缓存
        if (m_mountCache) {
            m_mountCache->refreshCache();
        }

        LogI("磁盘状态更新完成，更新了 %zu 个磁盘", updatedInfoMap.size());
    } else {
        LogW("批量更新磁盘状态时锁被占用");
    }
}

// ============================================================================
// 录像完成检查和磁盘切换控制
// ============================================================================

// 检查指定磁盘上的录像是否完成
bool DiskManager::isRecordingCompleteOnDisk(const std::string& diskPath)
{
    // 检查是否有正在进行的录像文件写入
    // 这里需要与录像模块配合，检查是否有活跃的录像文件句柄

    // 简化实现：检查最近是否有文件写入活动
    time_t now = time(NULL);
    const int RECORDING_IDLE_THRESHOLD = 30; // 30秒内无写入活动认为录像完成

    // 检查磁盘上最近修改的文件
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "find %s -name '*.h264' -o -name '*.idx' | head -10 | xargs stat -c %%Y 2>/dev/null | sort -nr | head -1", diskPath.c_str());

    FILE* fp = popen(cmd, "r");
    if (!fp) {
        LogW("无法检查录像文件状态: %s", diskPath.c_str());
        return true; // 无法检查时认为已完成
    }

    char result[64] = {0};
    if (fgets(result, sizeof(result), fp)) {
        time_t lastModified = atol(result);
        pclose(fp);

        if (now - lastModified > RECORDING_IDLE_THRESHOLD) {
            LogI("磁盘录像已完成: %s (最后修改: %ld秒前)", diskPath.c_str(), now - lastModified);
            return true;
        } else {
            LogI("磁盘录像进行中: %s (最后修改: %ld秒前)", diskPath.c_str(), now - lastModified);
            return false;
        }
    } else {
        pclose(fp);
        LogI("磁盘无录像文件，认为录像完成: %s", diskPath.c_str());
        return true;
    }
}

// 检查是否可以切换到指定磁盘
bool DiskManager::canSwitchToDisk(const std::string& targetDisk)
{
    // 检查目标磁盘是否已挂载且有足够空间
    if (!isMounted(targetDisk)) {
        LogW("目标磁盘未挂载: %s", targetDisk.c_str());
        return false;
    }

    if (isCriticalSpace(targetDisk)) {
        LogW("目标磁盘空间不足: %s", targetDisk.c_str());
        return false;
    }

    return true;
}

// 寻找下一个可用磁盘
std::string DiskManager::findNextAvailableDisk(const std::string& currentDisk)
{
    LogI("寻找下一个可用磁盘，当前磁盘: %s", currentDisk.c_str());

    // 按DISK_MAPPING顺序查找下一个可用磁盘
    auto diskMappings = getAllDiskMappings();
    std::vector<std::string> diskOrder;

    // 构建磁盘顺序列表
    for (const auto& mapping : diskMappings) {
        diskOrder.push_back(mapping.second + "/");  // 添加尾部斜杠以匹配路径格式
    }

    // 找到当前磁盘在序列中的位置
    int currentIndex = -1;
    for (size_t i = 0; i < diskOrder.size(); i++) {
        if (currentDisk.find(diskOrder[i].substr(0, diskOrder[i].length()-1)) != std::string::npos) {
            currentIndex = static_cast<int>(i);
            break;
        }
    }

    if (currentIndex == -1) {
        LogW("无法确定当前磁盘编号: %s", currentDisk.c_str());
        currentIndex = 0; // 默认从disk1开始
    }

    // 从下一个磁盘开始查找
    for (size_t i = 1; i < diskOrder.size(); i++) {
        int nextIndex = (currentIndex + static_cast<int>(i)) % static_cast<int>(diskOrder.size());
        std::string candidateDisk = diskOrder[nextIndex];

        LogI("检查候选磁盘: %s", candidateDisk.c_str());

        // 检查磁盘是否已挂载
        bool mounted = isMounted(candidateDisk);
        if (!mounted) {
            LogI("候选磁盘未挂载，尝试挂载: %s", candidateDisk.c_str());

            // 推断设备路径并尝试挂载（使用DISK_MAPPING）
            std::string devicePath = findDeviceByMountPath(candidateDisk);
            if (devicePath.empty()) {
                LogW("无法推断设备路径: %s", candidateDisk.c_str());
                continue;
            }

            // 尝试挂载
            bool mountSuccess = mountDiskInternal(devicePath);
            if (mountSuccess) {
                LogI("挂载成功: %s -> %s", devicePath.c_str(), candidateDisk.c_str());
                mounted = true;
            } else {
                LogW("挂载失败: %s -> %s", devicePath.c_str(), candidateDisk.c_str());
                continue;
            }
        }

        // 检查磁盘空间
        if (mounted && !isCriticalSpace(candidateDisk)) {
            // USB路径分离：确保不会选择USB设备作为录像磁盘
            if (isUsbDevicePath(candidateDisk)) {
                LogW("跳过USB设备，不能作为录像磁盘: %s", candidateDisk.c_str());
                continue;
            }

            LogI("找到可用磁盘: %s", candidateDisk.c_str());

            // 确保磁盘在管理列表中
            auto it = std::find(m_diskPaths.begin(), m_diskPaths.end(), candidateDisk);
            if (it == m_diskPaths.end()) {
                m_diskPaths.push_back(candidateDisk);
                LogI("将新发现的磁盘添加到管理列表: %s", candidateDisk.c_str());
            }

            return candidateDisk;
        } else if (mounted) {
            LogW("磁盘空间不足: %s", candidateDisk.c_str());
        }
    }

    LogW("没有找到可用的磁盘");
    return "";
}

// 查找回滚目标磁盘（当所有磁盘都满时使用）
std::string DiskManager::findRollbackDisk()
{
    LogI("查找回滚目标磁盘");

    // 按DISK_MAPPING顺序查找，优先选择第一个磁盘
    auto diskMappings = getAllDiskMappings();

    for (const auto& mapping : diskMappings) {
        std::string candidateDisk = mapping.second + "/";  // 添加尾部斜杠
        LogI("检查回滚候选磁盘: %s", candidateDisk.c_str());

        // 检查磁盘是否已挂载
        bool mounted = isMounted(candidateDisk);
        if (!mounted) {
            LogI("候选磁盘未挂载，尝试挂载: %s", candidateDisk.c_str());

            // 推断设备路径并尝试挂载（使用DISK_MAPPING）
            std::string devicePath = findDeviceByMountPath(candidateDisk);
            if (devicePath.empty()) {
                LogW("无法推断设备路径: %s", candidateDisk.c_str());
                continue;
            }

            // 尝试挂载
            bool mountSuccess = mountDiskInternal(devicePath);
            if (mountSuccess) {
                LogI("挂载成功: %s -> %s", devicePath.c_str(), candidateDisk.c_str());
                mounted = true;
            } else {
                LogW("挂载失败: %s -> %s", devicePath.c_str(), candidateDisk.c_str());
                continue;
            }
        }

        // 只要磁盘已挂载就可以作为回滚目标（即使空间不足，因为会边写边清理）
        if (mounted) {
            LogI("找到回滚目标磁盘: %s", candidateDisk.c_str());
            return candidateDisk;
        }
    }

    LogW("没有找到可用的回滚磁盘");
    return "";
}

// ============================================================================
// 清理完成检查方法
// ============================================================================

// 检查是否有清理正在进行
bool DiskManager::isCleanupInProgress() const
{
    // 对于const函数，直接读取原子变量，避免使用互斥锁
    // 这些变量的读取是原子的，不需要锁保护
    return m_cleanupRequested.load() || m_isCleaningUp.load();
}

// 等待清理完成
bool DiskManager::waitForCleanupComplete(int timeoutSeconds)
{
    LogI("等待清理完成，超时时间: %d秒", timeoutSeconds);

    time_t startTime = time(NULL);
    time_t endTime = startTime + timeoutSeconds;

    while (time(NULL) < endTime) {
        {
            ScopedLocker cleanupLock(m_cleanupMutex);

            // 检查清理是否完成
            if (!m_cleanupRequested.load() && !m_isCleaningUp.load()) {
                LogI("清理已完成");
                return true;
            }
        }

        // 等待100ms后再检查
        usleep(100000); // 100ms
    }

    LogW("等待清理完成超时: %d秒", timeoutSeconds);
    return false;
}



// ============================================================================
// DISK_MAPPING 辅助方法
// ============================================================================

// 根据挂载路径查找对应的设备路径
std::string DiskManager::findDeviceByMountPath(const std::string& mountPath)
{
    MOUNT_INFO diskMappings[] = DISK_MAPPING;
    int mappingCount = sizeof(diskMappings) / sizeof(diskMappings[0]);

    for (int i = 0; i < mappingCount; i++) {
        if (mountPath.find(diskMappings[i].mountpoint) != std::string::npos) {
            return std::string(diskMappings[i].device);
        }
    }

    return ""; // 未找到对应设备
}

// 获取所有磁盘映射关系
std::vector<std::pair<std::string, std::string>> DiskManager::getAllDiskMappings()
{
    std::vector<std::pair<std::string, std::string>> mappings;
    MOUNT_INFO diskMappings[] = DISK_MAPPING;
    int mappingCount = sizeof(diskMappings) / sizeof(diskMappings[0]);

    for (int i = 0; i < mappingCount; i++) {
        mappings.push_back(std::make_pair(
            std::string(diskMappings[i].device),
            std::string(diskMappings[i].mountpoint)
        ));
    }

    return mappings;
}

// 检查设备是否存在
bool DiskManager::isDeviceExists(const std::string& devicePath)
{
    if (devicePath.empty()) {
        return false;
    }

    // 使用access检查设备文件是否存在
    if (access(devicePath.c_str(), F_OK) == 0) {
        // 进一步检查是否是块设备
        struct stat st;
        if (stat(devicePath.c_str(), &st) == 0) {
            return S_ISBLK(st.st_mode);
        }
    }

    return false;
}

// 检查是否是移动设备
bool DiskManager::isRemovableDevice(const std::string& devicePath)
{
    if (devicePath.empty()) {
        return false;
    }

    // 提取设备名称（例如从 /dev/sda1 提取 sda）
    std::string deviceName;
    size_t lastSlash = devicePath.find_last_of('/');
    if (lastSlash != std::string::npos) {
        std::string fullName = devicePath.substr(lastSlash + 1);
        // 移除分区号，只保留设备名
        size_t i = 0;
        while (i < fullName.length() && !isdigit(fullName[i])) {
            i++;
        }
        deviceName = fullName.substr(0, i);
    } else {
        return false;
    }

    // 检查 /sys/block/设备名/removable 文件
    std::string removablePath = "/sys/block/" + deviceName + "/removable";
    FILE* fp = fopen(removablePath.c_str(), "r");
    if (fp) {
        char buffer[8];
        if (fgets(buffer, sizeof(buffer), fp)) {
            fclose(fp);
            return (buffer[0] == '1');
        }
        fclose(fp);
    }

    return false;
}

// 已删除getDiskMountBase函数

// 将磁盘路径转换为分区路径（带存在性检查）
std::string DiskManager::convertToPartitionPath(const std::string& devicePath)
{
    // 检查是否已经是分区路径
    if (devicePath.find("/dev/sd") == 0 && devicePath.length() > 8) {
        // 验证分区是否存在
        if (access(devicePath.c_str(), F_OK) == 0) {
            return devicePath;  // 分区存在，直接返回
        } else {
            LogW("分区不存在: %s", devicePath.c_str());
            return "";  // 分区不存在，返回空字符串
        }
    }
    if (devicePath.find("/dev/hd") == 0 && devicePath.length() > 8) {
        if (access(devicePath.c_str(), F_OK) == 0) {
            return devicePath;  // IDE分区存在
        } else {
            LogW("IDE分区不存在: %s", devicePath.c_str());
            return "";
        }
    }
    if (devicePath.find("/dev/nvme") == 0 && devicePath.find("p") != std::string::npos) {
        if (access(devicePath.c_str(), F_OK) == 0) {
            return devicePath;  // NVMe分区存在
        } else {
            LogW("NVMe分区不存在: %s", devicePath.c_str());
            return "";
        }
    }

    // 检查是否是整个磁盘路径，需要转换为分区
    std::string partitionPath;

    if ((devicePath.find("/dev/sd") == 0 && devicePath.length() == 8) ||      // SATA/SCSI
        (devicePath.find("/dev/hd") == 0 && devicePath.length() == 8)) {      // IDE
        partitionPath = devicePath + "1";
    } else if (devicePath.find("/dev/nvme") == 0 && devicePath.find("n") == devicePath.length() - 1) {  // NVMe
        partitionPath = devicePath + "p1";
    } else {
        // 无法识别的设备类型，检查原路径是否存在
        if (access(devicePath.c_str(), F_OK) == 0) {
            return devicePath;
        } else {
            LogW("无法识别的设备类型且设备不存在: %s", devicePath.c_str());
            return "";
        }
    }

    // 检查转换后的分区是否存在
    if (access(partitionPath.c_str(), F_OK) == 0) {
        LogI("设备路径转换: %s -> %s (分区存在)", devicePath.c_str(), partitionPath.c_str());
        return partitionPath;
    } else {
        LogW("转换后的分区不存在: %s -> %s", devicePath.c_str(), partitionPath.c_str());

        // 分区不存在，检查原始磁盘设备是否存在
        if (access(devicePath.c_str(), F_OK) == 0) {
            LogI("分区不存在但磁盘设备存在，返回原始路径: %s", devicePath.c_str());
            return devicePath;  // 返回原始磁盘路径
        } else {
            LogE("磁盘设备也不存在: %s", devicePath.c_str());
            return "";  // 设备完全不存在
        }
    }
}

// ============================================================================
// 外部调用封装函数（C风格接口）
// ============================================================================

// 用于保护磁盘管理器初始化的互斥锁
static TCSLock g_init_mutex;

// 确保磁盘管理器已初始化的辅助函数
static bool ensureDiskManagerInitialized()
{
    // 获取单例实例
    DiskManager* diskManager = DiskManager::getInstance();

    // 使用互斥锁保护初始化过程
    g_init_mutex.Enter();

    LogI("确保磁盘管理器已初始化...");

    try {

        // 初始化磁盘列表
        std::vector<std::string> diskPaths;
        MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;

        for (int i = 0; i < MAX_DISK_COUNT; i++) {
            const char* mountpoint = disks[i].mountpoint;
            if (mountpoint && *mountpoint) {
                diskPaths.push_back(mountpoint);
            }
        }

        // 初始化磁盘管理器（内部会检查是否已初始化）
        // diskManager->init(diskPaths);

        LogI("磁盘管理器初始化确认完成");
        g_init_mutex.Leave();
        return true;

    } catch (const std::exception& e) {
        LogE("磁盘管理器初始化异常: %s", e.what());
        g_init_mutex.Leave();
        return false;
    } catch (...) {
        LogE("磁盘管理器初始化发生未知异常");
        g_init_mutex.Leave();
        return false;
    }
}

// 挂载磁盘的外部接口
int vs_mount_disk(const char *device)
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogE("磁盘管理模块未初始化，请先调用 disk_module_initialize()");
        return FAIL;
    }

    return diskManager->mountDisk(device);
}

// 格式化磁盘的外部接口
int vs_format_disk(const char *disk)
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogE("磁盘管理模块未初始化，请先调用 disk_module_initialize()");
        return FAIL;
    }

    return diskManager->formatDisk(disk);
}

// 更新所有磁盘状态的外部接口
void vs_update_all_disk_status()
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogE("磁盘管理模块未初始化，请先调用 disk_module_initialize()");
        return;
    }

    diskManager->updateAllDiskStatus();
}

// 设置静默模式的外部接口
void vs_set_disk_silent_mode(bool enabled)
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogE("磁盘管理模块未初始化，请先调用 disk_module_initialize()");
        return;
    }

    diskManager->setSilentMode(enabled);
}

// 检查是否有可用磁盘的外部接口
bool vs_has_available_disk()
{
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogE("磁盘管理模块未初始化，请先调用 disk_module_initialize()");
        return false;
    }

    return diskManager->hasAvailableDisk();
}

// ========== 顶层初始化接口实现 ==========



/**
 * 执行阻塞式磁盘挂载初始化（重构版本）
 * 在主线程中同步执行所有磁盘挂载操作
 * 使用统一的磁盘检测和挂载接口
 *
 * @param diskManager 磁盘管理器实例
 * @return true 初始化成功, false 初始化失败
 */
static bool performBlockingDiskMountInitialization(DiskManager* diskManager)
{
    LogI("======== 开始阻塞式磁盘挂载初始化（重构版本） ========");

    // 加载UUID映射
    diskManager->getUUIDManager()->loadUUIDMappings();

    // 获取DISK_MAPPING中定义的所有磁盘配置
    MOUNT_INFO disks[MAX_DISK_COUNT] = DISK_MAPPING;

    // 挂载统计
    int totalDisks = 0;
    int existingDevices = 0;
    int alreadyMounted = 0;
    int successfulMounts = 0;
    int failedMounts = 0;

    std::vector<std::string> mountedDiskPaths;  // 成功挂载的磁盘路径

    LogI("遍历 DISK_MAPPING 配置，执行阻塞式挂载（使用统一接口）...");

    // 遍历所有DISK_MAPPING中的磁盘配置
    for (int i = 0; i < MAX_DISK_COUNT; i++) {
        if (disks[i].mountpoint && *disks[i].mountpoint &&
            disks[i].device && *disks[i].device) {

            totalDisks++;
            std::string mountPoint = disks[i].mountpoint;
            std::string devicePath = disks[i].device;

            LogI("处理磁盘 [%d/%d]: %s -> %s", i+1, MAX_DISK_COUNT,
                 devicePath.c_str(), mountPoint.c_str());

            // 检查是否是移动设备，跳过移动磁盘
            if (diskManager->isRemovableDevice(devicePath)) {
                LogI("跳过移动设备: %s", devicePath.c_str());
                continue;
            }

            // 检查设备是否存在
            if (!diskManager->isDeviceExists(devicePath)) {
                LogW("设备不存在，跳过: %s", devicePath.c_str());
                continue;
            }

            existingDevices++;

            // 检查设备是否已挂载到其他位置
            if (diskManager->isDeviceAlreadyMounted(devicePath)) {
                std::string currentMountPoint = diskManager->getDeviceMountPoint(devicePath);
                LogW("设备 %s 已挂载到其他位置: %s，跳过重复挂载到: %s",
                     devicePath.c_str(), currentMountPoint.c_str(), mountPoint.c_str());

                // 如果设备已挂载到目标位置，则添加到成功列表
                if (currentMountPoint == mountPoint) {
                    LogI("设备已正确挂载到目标位置: %s", mountPoint.c_str());
                    alreadyMounted++;
                    mountedDiskPaths.push_back(mountPoint);
                } else {
                    LogW("设备挂载位置不匹配，期望: %s，实际: %s",
                         mountPoint.c_str(), currentMountPoint.c_str());
                }
                continue;
            }

            // 检查目标挂载点是否已被其他设备占用
            if (diskManager->isMounted(mountPoint)) {
                LogW("挂载点 %s 已被其他设备占用，跳过挂载", mountPoint.c_str());
                continue;
            }

            LogI("执行阻塞挂载: %s -> %s", devicePath.c_str(), mountPoint.c_str());

            // 使用统一的挂载接口替换重复代码
            bool mountSuccess = diskManager->mountDiskInternal(devicePath);
            if (mountSuccess) {
                LogI("挂载成功: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                successfulMounts++;
                mountedDiskPaths.push_back(mountPoint);

                // 挂载成功后使用统一清盘触发检查
                diskManager->checkAndTriggerCleanupIfNeeded(mountPoint, "performBlockingDiskMountInitialization");

                // 挂载成功后延时500ms，确保挂载稳定性
                LogI("挂载延时500ms，确保多磁盘挂载稳定性...");
                Sleep(500);
            } else {
                LogE("挂载失败: %s -> %s", devicePath.c_str(), mountPoint.c_str());
                failedMounts++;
            }
        }
    }

    // 输出挂载统计信息
    LogI("======== 阻塞式挂载完成 ========");
    LogI("挂载统计: 总配置=%d, 存在设备=%d, 已挂载=%d, 新挂载=%d, 失败=%d",
         totalDisks, existingDevices, alreadyMounted, successfulMounts, failedMounts);

    // 更新磁盘管理器状态
    return finalizeDiskManagerInitialization(diskManager, mountedDiskPaths);
}

/**
 * 完成磁盘管理器初始化
 *
 * @param diskManager 磁盘管理器实例
 * @param mountedDiskPaths 已挂载的磁盘路径列表
 * @return true 初始化成功, false 初始化失败
 */
static bool finalizeDiskManagerInitialization(DiskManager* diskManager,
                                             const std::vector<std::string>& mountedDiskPaths)
{
    LogI("======== 完成磁盘管理器初始化（重构版本） ========");

    // 清空现有磁盘路径列表
    diskManager->clearDiskPaths();

    // 添加所有成功挂载的磁盘到管理列表，并检查磁盘状态
    for (const auto& path : mountedDiskPaths) {
        diskManager->addDiskPath(path);

        // 使用统一接口检查磁盘状态
        double usagePercent = diskManager->getDiskUsagePercent(path, true);
        LogI("添加磁盘到管理列表: %s (已用: %.2f%%)", path.c_str(), usagePercent);

        // 使用统一清盘触发检查
        diskManager->checkAndTriggerCleanupIfNeeded(path, "finalizeDiskManagerInitialization");
    }

    // 选择活动磁盘
    if (!mountedDiskPaths.empty()) {
        // 选择最小编号的磁盘作为活动磁盘
        std::string activeDisk = mountedDiskPaths[0];
        for (const auto& path : mountedDiskPaths) {
            if (path < activeDisk) {
                activeDisk = path;
            }
        }

        diskManager->setActiveDiskPathInternal(activeDisk);

        // 使用统一接口检查活动磁盘状态
        double activeDiskUsage = diskManager->getDiskUsagePercent(activeDisk, true);
        LogI("设置活动磁盘: %s (已用: %.2f%%)", activeDisk.c_str(), activeDiskUsage);

        // 设置模块初始化标记
        diskManager->setModuleInitialized(true);

        LogI("磁盘管理器初始化成功，共管理 %zu 个磁盘", mountedDiskPaths.size());
        return true;
    } else {
        LogW("没有可用的已挂载磁盘，启用静默模式");
        diskManager->setSilentMode(true);

        // 即使没有磁盘也标记为已初始化，避免重复初始化
        diskManager->setModuleInitialized(true);

        return true;  // 返回true，允许系统继续运行
    }
}

/**
* 磁盘管理模块顶层初始化函数
* 供主程序启动时调用，其他模块不得调用
*
* @param diskPaths 磁盘路径列表
* @return true 初始化成功, false 初始化失败
*/
bool disk_module_initialize()
{
	LogI("======== 磁盘管理模块顶层初始化开始 ========");
	LogI("执行阻塞式磁盘挂载策略 - 主线程同步挂载所有磁盘");

	// 获取磁盘管理器单例实例
	DiskManager* diskManager = DiskManager::getInstance();

	// 检查是否已经初始化过（在调用 initializeOnStartup 之前检查）
	if (diskManager->isModuleInitialized()) {
		LogW("磁盘管理模块已经初始化过，跳过重复初始化");
		return true;
	}

	// 执行磁盘管理启动时初始化（只在未初始化时调用）
	bool startupResult = diskManager->initializeOnStartup();
	if (!startupResult) {
		LogE("磁盘管理启动时初始化失败");
		return false;
	}

	// 执行阻塞式磁盘挂载初始化
	bool result = performBlockingDiskMountInitialization(diskManager);

	if (!result) {
		LogE("磁盘管理模块阻塞式初始化失败");
	}

	// 开启USB设备挂载
	UsbDeviceManager *UsbDev = diskManager->getUsbDeviceManager();
	if (UsbDev){
		UsbDev->startDetection();
		LogE("USB检查线程启动完成!");
	}
	
	LogI("======== 磁盘管理模块顶层初始化结束 ========");

	return result;
}



/**
 * 磁盘管理模块顶层清理函数
 * 供主程序退出时调用，其他模块不得调用
 */
void disk_module_cleanup()
{
    LogI("======== 磁盘管理模块顶层清理开始 ========");

    // 获取磁盘管理器单例实例
    DiskManager* diskManager = DiskManager::getInstance();
    if (!diskManager->isModuleInitialized()) {
        LogI("磁盘管理模块未初始化，无需清理");
        return;
    }

    // 销毁磁盘管理器单例实例
    DiskManager::destroyInstance();

    LogI("======== 磁盘管理模块顶层清理结束 ========");
}

/**
 * 检查磁盘管理模块是否已初始化
 *
 * @return true 已初始化, false 未初始化
 */
bool disk_module_is_initialized()
{
    return DiskManager::getInstance()->isModuleInitialized();
}


