// 多线程冲突场景详细分析
// 解释网络接收线程与UI线程的QString生命周期冲突

#include <QThread>
#include <QString>
#include <QMutex>
#include <QAtomicInt>
#include <thread>
#include <chrono>

/*
基于GDB堆栈的线程分析：

#9  vss_recv_thread(void*) from /opt/lib/libvsdev_netsdk.so
#8  read_cb(poll_event*, tagVSClient*) from /opt/lib/libvsdev_netsdk.so  
#7  MainWindow::dev_msg_cb (net_handle=0x44406a0, msg_code=2177, buf=0x7f68180bcc, len=256, context=0x4440470)

线程结构：
- 网络接收线程：vss_recv_thread() 
- 网络回调：read_cb() -> dev_msg_cb()
- UI线程：MainWindow对象所在线程
*/

// 1. 线程冲突的根本原因
class ThreadConflictAnalysis {
public:
    // QString的引用计数不是线程安全的
    static void explainRefCountingIssue() {
        /*
        QString内部使用QAtomicInt作为引用计数，虽然原子操作是线程安全的，
        但QString的整体操作不是线程安全的。
        
        问题场景：
        1. 网络线程创建QString对象
        2. 通过某种方式传递给UI线程
        3. 两个线程同时操作同一个QString对象
        4. 引用计数管理出现竞争条件
        */
        
        // 危险的跨线程操作示例
        QString shared_string;  // 共享的QString对象
        
        // 网络线程
        std::thread network_thread([&]() {
            char buffer[256] = "network data";
            shared_string = QString::fromUtf8(buffer, 256);  // 线程1修改
        });
        
        // UI线程
        std::thread ui_thread([&]() {
            QString copy = shared_string;  // 线程2读取，可能发生在线程1修改过程中
        });
        
        network_thread.join();
        ui_thread.join();
        // shared_string析构时可能崩溃
    }
};

// 2. 具体的冲突时序分析
class ConflictTimingAnalysis {
public:
    // 场景1：构造过程中的冲突
    static void constructionConflict() {
        /*
        时序问题：
        
        时刻T1: 网络线程调用dev_msg_cb
        时刻T2: 创建QString message = QString::fromUtf8(buf, len)
        时刻T3: QString构造过程中，分配QTypedArrayData
        时刻T4: 网络线程接收到新数据，修改buf内容
        时刻T5: QString构造完成，但内部数据可能不一致
        时刻T6: 函数结束，message析构 -> 崩溃
        */
        
        char* volatile_buffer = new char[256];
        strcpy(volatile_buffer, "initial data");
        
        // 模拟网络线程不断修改缓冲区
        std::thread modifier([&]() {
            for (int i = 0; i < 1000; ++i) {
                memset(volatile_buffer, 'A' + (i % 26), 256);
                std::this_thread::sleep_for(std::chrono::microseconds(1));
            }
        });
        
        // 模拟回调线程创建QString
        std::thread callback([&]() {
            for (int i = 0; i < 100; ++i) {
                try {
                    QString message = QString::fromUtf8(volatile_buffer, 256);
                    // message析构时可能崩溃
                } catch (...) {
                    // 异常处理
                }
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
        
        modifier.join();
        callback.join();
        delete[] volatile_buffer;
    }
    
    // 场景2：析构过程中的冲突
    static void destructionConflict() {
        /*
        问题：多个线程同时析构共享QString数据
        
        时序：
        T1: 网络线程创建QString A
        T2: QString A被复制到UI线程 (QString B)
        T3: A和B共享同一个QTypedArrayData
        T4: 网络线程函数结束，A析构，ref.deref()
        T5: 同时UI线程也在析构B，ref.deref()
        T6: 引用计数变成负数或其他异常值
        T7: deallocate()检测到异常 -> abort()
        */
        
        QAtomicInt shared_ref(2);  // 模拟共享引用计数
        
        std::thread thread1([&]() {
            // 模拟QString析构
            int old_ref = shared_ref.fetchAndSubtract(1);
            if (old_ref == 1) {
                // 最后一个引用，应该释放内存
                // 但可能与thread2发生竞争
            }
        });
        
        std::thread thread2([&]() {
            // 同时进行的析构
            int old_ref = shared_ref.fetchAndSubtract(1);
            if (old_ref == 1) {
                // 两个线程都认为自己是最后一个引用
                // 导致双重释放
            }
        });
        
        thread1.join();
        thread2.join();
    }
};

// 3. MainWindow对象的线程安全问题
class MainWindowThreadSafety {
private:
    QString m_lastMessage;  // 成员变量
    QMutex m_mutex;
    
public:
    // 危险的跨线程访问
    void unsafeDevMsgCb(void* net_handle, int msg_code, char* buf, int len, void* context) {
        // 这个函数在网络线程中被调用
        MainWindow* window = static_cast<MainWindow*>(context);
        
        // 危险操作1：直接修改UI线程的成员变量
        QString message = QString::fromUtf8(buf, len);
        window->m_lastMessage = message;  // 跨线程写入
        
        // 危险操作2：直接调用UI函数
        window->updateStatusBar(message);  // UI函数在网络线程中调用
        
        // 危险操作3：发射信号但没有正确的连接类型
        emit window->messageReceived(message);  // 可能导致跨线程调用
    }
    
    // 安全的跨线程访问
    void safeDevMsgCb(void* net_handle, int msg_code, char* buf, int len, void* context) {
        // 参数验证
        if (!buf || !context || len <= 0) return;
        
        MainWindow* window = static_cast<MainWindow*>(context);
        
        // 安全操作1：创建数据副本
        QByteArray safeData(buf, len);
        
        // 安全操作2：使用Qt的线程安全机制
        QMetaObject::invokeMethod(window,
            [window, safeData, msg_code]() {
                // 这个lambda在UI线程中执行
                QString message = QString::fromUtf8(safeData);
                window->handleNetworkMessage(message, msg_code);
            },
            Qt::QueuedConnection  // 队列连接，线程安全
        );
    }
    
    void updateStatusBar(const QString& message) {
        // UI更新函数，必须在UI线程中调用
        if (QThread::currentThread() != this->thread()) {
            qWarning("updateStatusBar called from wrong thread!");
            return;
        }
        // 安全的UI更新
    }
    
    void handleNetworkMessage(const QString& message, int msgCode) {
        // 线程安全的消息处理
        QMutexLocker locker(&m_mutex);
        m_lastMessage = message;
        updateStatusBar(message);
    }
    
signals:
    void messageReceived(const QString& message);
};

// 4. 网络SDK的线程模型分析
class NetworkSDKAnalysis {
public:
    /*
    基于堆栈信息的分析：
    
    #9 vss_recv_thread(void*) - 网络接收线程主函数
    #8 read_cb(poll_event*, tagVSClient*) - 数据读取回调
    #7 MainWindow::dev_msg_cb - 用户回调函数
    
    线程模型：
    1. 网络SDK创建独立的接收线程 (vss_recv_thread)
    2. 当有数据到达时，在网络线程中调用read_cb
    3. read_cb进一步调用用户注册的回调函数dev_msg_cb
    4. dev_msg_cb在网络线程中执行，但操作UI线程的对象
    
    问题：
    - 回调函数在网络线程中执行
    - 但MainWindow对象属于UI线程
    - 直接操作QString等Qt对象不安全
    */
    
    // 模拟网络SDK的线程模型
    static void simulateNetworkSDK() {
        MainWindow* window = new MainWindow();
        
        // 模拟网络接收线程
        std::thread network_thread([window]() {
            char buffer[256];
            
            while (true) {
                // 模拟接收网络数据
                memset(buffer, 'A', 255);
                buffer[255] = '\0';
                
                // 在网络线程中调用回调函数
                MainWindow::dev_msg_cb(nullptr, 2177, buffer, 256, window);
                
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        });
        
        // UI线程继续运行
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        network_thread.detach();
        delete window;
    }
};

// 5. 解决方案：线程安全的消息传递
class ThreadSafeMessaging {
private:
    struct NetworkMessage {
        QByteArray data;
        int msgCode;
        qint64 timestamp;
        
        NetworkMessage(const char* buf, int len, int code)
            : data(buf, len), msgCode(code), timestamp(QDateTime::currentMSecsSinceEpoch()) {}
    };
    
    static QQueue<NetworkMessage> s_messageQueue;
    static QMutex s_queueMutex;
    
public:
    // 线程安全的回调函数
    static void threadSafeDevMsgCb(void* net_handle, int msg_code, char* buf, int len, void* context) {
        if (!buf || !context || len <= 0) return;
        
        try {
            // 在网络线程中只做数据复制
            NetworkMessage msg(buf, len, msg_code);
            
            {
                QMutexLocker locker(&s_queueMutex);
                s_messageQueue.enqueue(msg);
                
                // 限制队列大小
                while (s_messageQueue.size() > 1000) {
                    s_messageQueue.dequeue();
                }
            }
            
            // 通知UI线程处理消息
            MainWindow* window = static_cast<MainWindow*>(context);
            QMetaObject::invokeMethod(window, "processMessageQueue", Qt::QueuedConnection);
            
        } catch (const std::exception& e) {
            qCritical("threadSafeDevMsgCb exception: %s", e.what());
        }
    }
    
    // UI线程中的消息处理
    static void processMessageQueue(MainWindow* window) {
        QMutexLocker locker(&s_queueMutex);
        
        while (!s_messageQueue.isEmpty()) {
            NetworkMessage msg = s_messageQueue.dequeue();
            
            // 在UI线程中安全地创建QString
            QString text = QString::fromUtf8(msg.data);
            window->handleNetworkMessage(text, msg.msgCode);
        }
    }
};

QQueue<ThreadSafeMessaging::NetworkMessage> ThreadSafeMessaging::s_messageQueue;
QMutex ThreadSafeMessaging::s_queueMutex;

/*
总结：多线程冲突的具体机制

1. 线程边界问题
   - dev_msg_cb在网络线程中执行
   - 但操作UI线程的MainWindow对象
   - QString等Qt对象不是线程安全的

2. 引用计数竞争
   - 多个线程同时修改QString的引用计数
   - 导致引用计数异常或双重释放

3. 内存可见性问题
   - 网络线程修改的数据对UI线程不可见
   - 或者修改过程中被其他线程观察到中间状态

4. 生命周期管理错误
   - 对象在一个线程中创建，在另一个线程中销毁
   - 生命周期管理不当导致内存错误

解决方案的核心是：
- 避免跨线程直接操作Qt对象
- 使用Qt的线程安全机制（QMetaObject::invokeMethod）
- 在网络线程中只做数据复制，在UI线程中处理业务逻辑
*/
